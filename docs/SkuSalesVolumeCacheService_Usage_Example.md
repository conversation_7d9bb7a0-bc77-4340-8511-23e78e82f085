# SkuSalesVolumeCacheService 使用示例

## 概述

`SkuSalesVolumeCacheService` 是一个用于从Redis批量获取SKU销售GMV数据的缓存服务。它使用Redis管道技术提高批量查询性能，并提供内存缓存以进一步优化访问速度。

## 主要特性

- **Redis缓存**: 从Redis获取真实的SKU销售GMV数据
- **批量查询**: 支持批量获取多个SKU的销售数据，使用Redis管道技术
- **内存缓存**: 5分钟内存缓存，减少Redis访问频率
- **高精度数据**: 返回BigDecimal类型，支持高精度的GMV计算
- **缓存key格式**: `mall_search_rerank_sku_gmv_{sku}`
- **零值处理**: 如果Redis中没有数据，返回BigDecimal.ZERO而不是null，避免空指针异常

## 使用方法

### 1. 注入服务

```java
@Autowired
private SkuSalesVolumeCacheService skuSalesVolumeCacheService;
```

### 2. 获取单个SKU的销售GMV

```java
// 获取单个SKU的销售GMV数据
String sku = "1001450537647";
BigDecimal gmv = skuSalesVolumeCacheService.getSkuSalesVolume(sku);

// 注意：现在不会返回null，没有数据时返回BigDecimal.ZERO
if (gmv.compareTo(BigDecimal.ZERO) > 0) {
    System.out.println("SKU " + sku + " 的销售GMV: " + gmv);
} else {
    System.out.println("SKU " + sku + " 没有销售GMV数据（返回0）");
}
```

### 3. 批量获取SKU的销售GMV

```java
// 使用Set参数
Set<String> skuSet = Sets.newHashSet("1001450537647", "1001450537648", "1001450537649");
Map<String, BigDecimal> gmvMap = skuSalesVolumeCacheService.getSkuSalesVolumeBatch(skuSet);

for (Map.Entry<String, BigDecimal> entry : gmvMap.entrySet()) {
    System.out.println("SKU " + entry.getKey() + " 的销售GMV: " + entry.getValue());
}

// 使用List参数
List<String> skuList = Arrays.asList("1001450537647", "1001450537648", "1001450537649");
Map<String, BigDecimal> gmvMap2 = skuSalesVolumeCacheService.getSkuSalesVolumeBatch(skuList);
```

### 4. 检查SKU是否有销售数据

```java
// 检查单个SKU
String sku = "1001450537647";
boolean hasData = skuSalesVolumeCacheService.hasSkuSalesVolume(sku);
System.out.println("SKU " + sku + " 是否有销售数据: " + hasData);

// 批量检查
Set<String> skuSet = Sets.newHashSet("1001450537647", "1001450537648", "1001450537649");
Map<String, Boolean> hasDataMap = skuSalesVolumeCacheService.hasSkuSalesVolumeBatch(skuSet);

for (Map.Entry<String, Boolean> entry : hasDataMap.entrySet()) {
    System.out.println("SKU " + entry.getKey() + " 是否有销售数据: " + entry.getValue());
}
```

## 在搜索重排序中的应用

### 与ProductSearchBatchDisperseService集成

```java
@Autowired
private ProductSearchBatchDisperseService batchDisperseService;

@Autowired
private SkuSalesVolumeCacheService skuSalesVolumeCacheService;

// 使用默认的重排序功能（包含库存、销量、类目）
// 新版本会自动在rerank前批量获取所有SKU的销量数据，优化Redis网络请求
List<EsMarketItemInfoDTO> skuList = getSearchResults();
List<EsMarketItemInfoDTO> rerankedList = batchDisperseService.batchDisperseSkusWithDefaultStock(
    skuList, 20);

// 自定义销量函数
SkuSalesVolumeFunction customSalesFunction = sku -> 
    skuSalesVolumeCacheService.getSkuSalesVolume(sku.getItemCode());

SkuStockFunction stockFunction = sku -> 
    sku.getStoreQuantity() != null && sku.getStoreQuantity() > 0;

SkuCategoryFunction categoryFunction = EsMarketItemInfoDTO::getCategoryId;

List<EsMarketItemInfoDTO> customRerankedList = batchDisperseService.batchDisperseSkus(
    skuList, stockFunction, 20, customSalesFunction, categoryFunction);
```

### 在商品推荐中的应用

```java
// 根据销售GMV对商品进行排序
List<String> productSkus = getProductSkus();
Map<String, BigDecimal> gmvData = skuSalesVolumeCacheService.getSkuSalesVolumeBatch(productSkus);

// 按销售GMV倒序排序
// 注意：现在不会有null值，没有数据的SKU会返回BigDecimal.ZERO
List<String> sortedSkus = productSkus.stream()
    .sorted((sku1, sku2) -> {
        BigDecimal gmv1 = gmvData.getOrDefault(sku1, BigDecimal.ZERO);
        BigDecimal gmv2 = gmvData.getOrDefault(sku2, BigDecimal.ZERO);

        return gmv2.compareTo(gmv1); // 倒序排序
    })
    .collect(Collectors.toList());
```

## 性能优化建议

### 1. 批量查询优化

```java
// 推荐：使用批量查询
Set<String> skuSet = getAllSkus();
Map<String, BigDecimal> gmvMap = skuSalesVolumeCacheService.getSkuSalesVolumeBatch(skuSet);

// 不推荐：逐个查询
for (String sku : skuSet) {
    BigDecimal gmv = skuSalesVolumeCacheService.getSkuSalesVolume(sku); // 会产生多次Redis访问
}
```

### 2. 缓存预热

```java
// 在系统启动或定时任务中预热热门SKU的缓存
@PostConstruct
public void warmUpCache() {
    Set<String> hotSkus = getHotSkus(); // 获取热门SKU列表
    skuSalesVolumeCacheService.getSkuSalesVolumeBatch(hotSkus);
}
```

### 3. 异常处理

```java
try {
    Map<String, BigDecimal> gmvMap = skuSalesVolumeCacheService.getSkuSalesVolumeBatch(skuSet);
    // 处理业务逻辑
} catch (Exception e) {
    log.error("获取SKU销售GMV数据失败", e);
    // 降级处理，使用默认排序或其他策略
}
```

## 注意事项

1. **数据类型**: 返回的是BigDecimal类型，没有数据时返回BigDecimal.ZERO而不是null
2. **缓存时间**: 内存缓存5分钟，Redis缓存30分钟
3. **批量大小**: 建议单次批量查询的SKU数量不超过1000个
4. **Redis key**: 确保Redis中存在对应的key，格式为`mall_search_rerank_sku_gmv_{sku}`
5. **异常处理**: 服务内部会处理异常，但建议在业务层也添加异常处理逻辑
6. **性能优化**: 新版本的ProductSearchBatchDisperseService会在rerank前自动批量获取所有SKU的销量数据，大幅减少Redis网络请求

## 监控和日志

服务会输出以下日志信息：
- 批量查询的SKU数量
- Redis查询结果统计
- 数据格式错误警告
- 缓存命中情况（通过@InMemoryCache注解）

建议在生产环境中监控这些日志，以便及时发现和解决问题。
