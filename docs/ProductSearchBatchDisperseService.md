# ProductSearchBatchDisperseService 实现文档

## 概述

本文档描述了 `ProductSearchBatchDisperseService` 的实现，这是 Python `batch_disperse_skus` 功能的 Java 版本。该服务用于分段重排序 SKU 列表，将每段内无库存的 SKU 排到段末，并可选按销量倒序排序。

## 核心功能

### 主要方法

#### `batchDisperseSkus`
```java
public List<EsMarketItemInfoDTO> batchDisperseSkus(
    List<EsMarketItemInfoDTO> skuList,
    SkuStockFunction stockFunction,
    int batchSize,
    SkuSalesVolumeFunction salesVolumeFunction,
    SkuCategoryFunction categoryFunction)
```

**参数说明：**
- `skuList`: 待处理的 SKU 列表
- `stockFunction`: 判断 SKU 是否有库存的函数，返回 true 表示有库存
- `batchSize`: 每段 SKU 数量
- `salesVolumeFunction`: 获取 SKU 销量的函数，返回销量数值
- `categoryFunction`: 获取 SKU 类目的函数，返回类目标识

**处理逻辑：**
1. 按 `batchSize` 将 SKU 列表分段
2. 对每段内的 SKU 进行重排序：
   - 如果提供了库存函数，优先按库存状态分组（有库存在前）
   - 如果同时提供了类目和销量函数，按类目分组后组内按销量倒序排序
   - 如果只提供了销量函数，仅按销量倒序排序
   - 其他情况保持原有顺序

### 便捷方法

#### `batchDisperseSkusWithDefaultStock`
使用默认的库存检查函数进行批量分散重排序，支持核心客户和普通客户的库存区分。

#### `batchDisperseSkusBySalesOnly`
仅按销量进行批量分散重排序，不考虑库存和类目。

#### `batchDisperseSkusByStockOnly`
仅按库存进行批量分散重排序，不考虑销量和类目。

## 函数式接口

### SkuStockFunction
```java
@FunctionalInterface
public interface SkuStockFunction extends Function<EsMarketItemInfoDTO, Boolean> {
    Boolean apply(EsMarketItemInfoDTO sku);
}
```
用于判断 SKU 是否有库存。

### SkuSalesVolumeFunction
```java
@FunctionalInterface
public interface SkuSalesVolumeFunction extends Function<EsMarketItemInfoDTO, Long> {
    Long apply(EsMarketItemInfoDTO sku);
}
```
用于获取 SKU 销量数据。

### SkuCategoryFunction
```java
@FunctionalInterface
public interface SkuCategoryFunction extends Function<EsMarketItemInfoDTO, Long> {
    Long apply(EsMarketItemInfoDTO sku);
}
```
用于获取 SKU 类目信息。

## 销量缓存服务

### SkuSalesVolumeCacheService
提供 SKU 销售GMV数据的缓存服务，从Redis批量获取真实的销售数据，包含以下功能：
- `getSkuSalesVolume(String sku)`: 获取单个 SKU 的销售GMV数据
- `getSkuSalesVolumeBatch(Set<String> skuList)`: 批量获取 SKU 销售GMV数据
- `getSkuSalesVolumeBatch(List<String> skuList)`: 批量获取 SKU 销售GMV数据（List参数版本）
- `hasSkuSalesVolume(String sku)`: 检查SKU是否有销售GMV数据
- `hasSkuSalesVolumeBatch(Set<String> skuList)`: 批量检查SKU是否有销售GMV数据

**Redis缓存特性：**
- Redis key格式：`mall_search_rerank_sku_gmv_{sku}`，其中{sku}是SKU编码
- 使用Redis管道技术进行批量查询，提高性能
- 内存缓存5分钟，Redis缓存30分钟
- 返回BigDecimal类型的GMV数据，支持高精度计算
- 没有数据时返回BigDecimal.ZERO而不是null，避免空指针异常

**性能优化：**
- 在rerank开始前，会一次性批量获取所有SKU的销量数据，大幅减少Redis网络请求
- 使用预先获取的销量缓存进行排序，避免在排序过程中重复调用Redis

## 集成使用

### 在 ProductSearchRerankServiceImpl 中的集成

服务已经集成到 `ProductSearchRerankServiceImpl` 中，可以通过取消注释相关代码来启用：

```java
if (esMarketItemInfoDTOs.size() > 20) {
    long batchStartedAt = System.currentTimeMillis();
    log.info("开始批量分散重排序，SKU总数: {}", esMarketItemInfoDTOs.size());
    boolean isCoreCustomer = withCore;
    esMarketItemInfoDTOs = productSearchBatchDisperseService.batchDisperseSkusWithDefaultStock(
            esMarketItemInfoDTOs, 20, isCoreCustomer);
    log.info("批量分散重排序耗时:{}ms, SKU个数:{}", System.currentTimeMillis() - batchStartedAt, esMarketItemInfoDTOs.size());
}
```

**注意**: 原有的 SPU 分散实验相关代码（`spuDisperseIntervalLow` 和 `spuDisperseIntervalHigh`）已被移除，因为该实验不成功。现在的实现更加简洁，专注于基于库存、销量和类目的批量重排序功能。

### 使用示例

```java
@Autowired
private ProductSearchBatchDisperseService batchDisperseService;

// 使用默认库存检查的完整功能
List<EsMarketItemInfoDTO> result = batchDisperseService.batchDisperseSkusWithDefaultStock(
    skuList, 20, false);

// 仅按销量排序
List<EsMarketItemInfoDTO> result = batchDisperseService.batchDisperseSkusBySalesOnly(
    skuList, 20);

// 自定义函数
SkuStockFunction customStockFunction = sku -> sku.getStoreQuantity() > 10;
SkuSalesVolumeFunction customSalesFunction = sku -> getSalesFromExternalService(sku.getItemCode());
SkuCategoryFunction customCategoryFunction = EsMarketItemInfoDTO::getCategoryId;

List<EsMarketItemInfoDTO> result = batchDisperseService.batchDisperseSkus(
    skuList, customStockFunction, 20, customSalesFunction, customCategoryFunction);
```

## 异常处理

- 所有排序过程中的异常都会被捕获并记录日志，不会影响整体流程
- 如果获取类目失败，SKU 会被放入默认类目（-1L）
- 如果获取销量失败，比较时返回 0（保持原有顺序）

## 性能考虑

- 使用 `LinkedHashMap` 保持类目的原有顺序
- 批量处理减少内存占用
- 异常处理确保服务的稳定性
- 日志记录便于性能监控和问题排查

## 测试

提供了完整的单元测试 `ProductSearchBatchDisperseServiceTest`，覆盖了各种使用场景：
- 完整功能测试
- 便捷方法测试
- 边界条件测试（空列表、无效批次大小等）

## 配置和扩展

- 销量缓存服务可以替换为实际的数据源
- 函数式接口设计便于扩展和自定义
- 服务可以独立使用，也可以集成到现有的搜索重排序流程中
