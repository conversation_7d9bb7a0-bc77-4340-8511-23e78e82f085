# 批量SKU到手价查询API文档

## 概述

该API允许通过门店ID和SKU列表查询指定门店对于这些SKU的实际到手价。API无需登录，但使用对称加密签名来防止滥用。

## 接口信息

- **URL**: `/price/batch/take-actual-price`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **认证**: 签名验证（无需登录）

## 请求参数

### 请求体结构

```json
{
  "mId": 12345,
  "skuList": [
    {
      "sku": "863080734120",
      "quantity": 2
    },
    {
      "sku": "593305756483",
      "quantity": 1
    }
  ],
  "timestamp": 1703123456789,
  "signature": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mId | Long | 是 | 门店ID |
| skuList | Array | 是 | SKU列表，最多20个 |
| skuList[].sku | String | 是 | SKU编码 |
| skuList[].quantity | Integer | 是 | 数量 |
| timestamp | Long | 是 | 请求时间戳（毫秒），用于防重放攻击 |
| signature | String | 是 | 签名，用于验证请求合法性 |

### 限制条件

- SKU列表最多支持20个
- 时间戳有效期为5分钟
- 签名必须正确

## 签名算法

### 签名生成步骤

1. **获取当前日期字符串**：获取当前日期的 `yyyyMMdd` 格式字符串。
   ```
   例如：20250701
   ```

2. **构建待签名字符串**：将 `SIGN_KEY`（即 `summerfarm-mall`）与日期字符串拼接。
   ```
   signData = SIGN_KEY + yyyyMMdd
   例如：summerfarm-mall20250701
   ```

3. **生成签名**：对待签名字符串进行MD5加密。
   ```
   signature = MD5(signData)
   ```

### Java代码示例

```java
import org.springframework.util.DigestUtils;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

public class SignatureExample {
    private static final String SIGN_KEY = "summerfarm-mall";

    public static String generateSignature() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String today = sdf.format(new Date());
        String signData = SIGN_KEY + today;
        return DigestUtils.md5DigestAsHex(signData.getBytes(StandardCharsets.UTF_8));
    }

    public static void main(String[] args) {
        String signature = generateSignature();
        System.out.println("签名: " + signature);
    }
}
```

### Python代码示例

```python
import hashlib
import datetime

def generate_signature():
    sign_key = "summerfarm-mall"
    today = datetime.datetime.now().strftime("%Y%m%d")
    sign_data = sign_key + today
    return hashlib.md5(sign_data.encode('utf-8')).hexdigest()

# 使用示例
signature = generate_signature()
print(f"签名: {signature}")
```

## 响应结果

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "mId": 12345,
    "merchantName": "测试门店",
    "isMajorMerchant": false,
    "businessLine": 0,
    "queryTime": 1703123456789,
    "skuPrices": [
      {
        "sku": "863080734120",
        "quantity": 2,
        "originalPrice": 10.50,
        "takeActualPrice": 9.80,
        "discountAmount": 1.40,
        "isValid": true,
        "productName": "测试商品A",
        "specification": "500g",
        "unit": "袋"
      },
      {
        "sku": "593305756483",
        "quantity": 1,
        "originalPrice": 15.00,
        "takeActualPrice": 15.00,
        "discountAmount": 0.00,
        "isValid": true,
        "productName": "测试商品B",
        "specification": "1kg",
        "unit": "包"
      }
    ]
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.mId | Long | 门店ID |
| data.merchantName | String | 门店名称 |
| data.isMajorMerchant | Boolean | 是否大客户 |
| data.businessLine | Integer | 业务线：0=鲜沐;1=pop |
| data.queryTime | Long | 查询时间戳 |
| data.skuPrices | Array | SKU价格列表 |
| skuPrices[].sku | String | SKU编码 |
| skuPrices[].quantity | Integer | 数量 |
| skuPrices[].originalPrice | BigDecimal | 原价（单价） |
| skuPrices[].takeActualPrice | BigDecimal | 到手价（单价） |
| skuPrices[].discountAmount | BigDecimal | 优惠金额（总计） |
| skuPrices[].isValid | Boolean | 是否有效 |
| skuPrices[].errorMessage | String | 错误信息（当isValid为false时） |
| skuPrices[].productName | String | 商品名称 |
| skuPrices[].specification | String | 规格 |
| skuPrices[].unit | String | 单位 |

### 错误响应

```json
{
  "success": false,
  "message": "签名验证失败",
  "data": null
}
```

## 错误码说明

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 签名验证失败 | 签名不正确或时间戳过期 | 检查签名算法和时间戳 |
| 门店ID不能为空 | mId参数缺失 | 提供有效的门店ID |
| SKU列表不能为空 | skuList参数缺失或为空 | 提供有效的SKU列表 |
| SKU列表最多支持20个 | SKU数量超过限制 | 减少SKU数量到20个以内 |
| 商户不存在 | 指定的门店ID不存在 | 检查门店ID是否正确 |

## 使用示例

### cURL示例

```bash
# 批量查询SKU到手价
# POST https://qah5.summerfarm.net/price/batch/take-actual-price

# --- 配置 ---
# 签名密钥 (根据 BatchPriceQueryServiceImpl.java)
SIGN_KEY="summerfarm-mall"
# QA 环境的 API 地址
# API_URL="https://qah5.summerfarm.net/price/batch/take-actual-price"
API_URL="http://127.0.0.1/price/batch/take-actual-price"

# --- 脚本 ---
# 1. 生成签名
# 获取 yyyyMMdd 格式的当天日期
TODAY=$(date +%Y%m%d)
# 使用 echo -n 防止产生新行，并用 md5 (适用于 macOS/darwin) 生成签名
SIGNATURE=$(echo -n "${SIGN_KEY}${TODAY}" | md5)

# (可选) 打印生成的签名和时间戳用于调试
# echo "Timestamp: $(date +%s)"
# echo "Signature: $SIGNATURE"

# 2. 构建 JSON 请求体
# 使用 cat 和 EOF 来创建一个多行的 JSON 字符串，更清晰
read -r -d '' JSON_PAYLOAD << EOM
{
    "mId": 349548,
    "skuList": [
        {"sku": "561568850574", "quantity": 1},
        {"sku": "561568850744", "quantity": 1},
        {"sku": "561568850421", "quantity": 1}
    ],
    "signature": "$SIGNATURE",
    "timestamp": $(date +%s)
}
EOM

# 3. 发送请求
# -H: 添加从参考命令中看到的 authority 头
# --data-binary: 使用 binary 模式发送数据
# --compressed: 请求压缩过的响应
curl -v "$API_URL" \
  -H 'authority: qah5.summerfarm.net' \
  -H 'Content-Type: application/json' \
  --data-binary "$JSON_PAYLOAD" \
  --compressed
```

### JavaScript示例

```javascript
const crypto = require('crypto');

function generateSignature(mId, skuList, timestamp) {
    const secretKey = 'summerfarm-mall';
    const signData = `${mId}|${skuList}|${timestamp}|${secretKey}`;
    return crypto.createHash('md5').update(signData).digest('hex');
}

async function queryBatchPrice() {
    const mId = 12345;
    const skuList = '863080734120:2,593305756483:1';
    const timestamp = Date.now();
    const signature = generateSignature(mId, skuList, timestamp);
    
    const requestBody = {
        mId: mId,
        skuList: [
            { sku: '863080734120', quantity: 2 },
            { sku: '593305756483', quantity: 1 }
        ],
        timestamp: timestamp,
        signature: signature
    };
    
    const response = await fetch('/price/batch/take-actual-price', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });
    
    const result = await response.json();
    console.log(result);
}
```

## 注意事项

1. **时间同步**：确保客户端时间与服务器时间同步，时间戳误差不能超过5分钟
2. **签名安全**：密钥 `summerfarm-mall` 需要妥善保管，不要在客户端代码中暴露
3. **请求频率**：建议控制请求频率，避免对服务器造成压力
4. **大客户处理**：大客户不返回到手价信息，会返回空的价格列表
5. **SKU有效性**：不存在或不可购买的SKU会在结果中标记为无效

## 版本历史

- v1.0.0 (2024-06-27): 初始版本，支持批量SKU到手价查询
