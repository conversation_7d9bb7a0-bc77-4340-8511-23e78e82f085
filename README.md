# Spring Boot Unused Code Analyzer

这个工具用于分析Spring Boot项目中哪些类和方法没有被实际调用。它特别考虑了Spring Boot的特性，如组件扫描、依赖注入和注解驱动的开发模式。

## 功能特点

- 识别未被使用的类和方法
- 排除仅在测试代码中使用的类和方法
- 考虑Spring Boot特有的模式（如@Service, @Controller等注解）
- 识别通过依赖注入使用的组件
- 识别通过Spring框架调用的方法（如@RequestMapping, @Scheduled等）

## 构建和运行

### 前提条件

- Java 8 或更高版本
- Maven

### 使用脚本运行（推荐）

我们提供了两个脚本来简化构建和运行过程：

#### Linux/macOS用户

```bash
# 给脚本添加执行权限
chmod +x run-analyzer.sh

# 运行脚本
./run-analyzer.sh
```

#### Windows用户

```
run-analyzer.bat
```

这些脚本会自动构建分析器并在当前项目上运行它，然后将结果保存到`unused-code-report.txt`文件中。

### 手动构建和运行

如果你想手动构建和运行分析器，请按照以下步骤操作：

1. 将`UnusedCodeAnalyzer.java`和`analyzer-pom.xml`文件放在一个空目录中
2. 重命名`analyzer-pom.xml`为`pom.xml`
3. 执行以下命令构建项目：

```bash
mvn clean package
```

这将创建一个包含所有依赖的可执行JAR文件。

4. 将生成的JAR文件复制到你想要分析的Spring Boot项目的根目录
5. 在项目根目录中执行：

```bash
java -jar code-analyzer-1.0.0-jar-with-dependencies.jar
```

## 解读结果

分析器会输出两类结果：

1. **未使用的类**：这些类在实际代码中没有被引用，或者仅在测试代码中被引用
2. **未使用的方法**：这些方法在实际代码中没有被调用，或者仅在测试代码中被调用

每个结果都会显示完整的类名或方法签名，以及它们在项目中的文件路径。

## 注意事项和局限性

1. **动态调用**：该工具可能无法检测通过反射、动态代理或其他运行时机制调用的代码
2. **外部调用**：如果类或方法是为了被项目外部调用（如API端点、公共库），它们可能被错误地标记为未使用
3. **间接使用**：某些通过配置或约定使用的代码可能被错误地标记为未使用
4. **解析限制**：复杂的泛型类型、lambda表达式和方法引用可能无法被完全解析
5. **Spring Boot特性**：尽管工具尝试考虑Spring Boot的特性，但可能无法覆盖所有Spring Boot的使用模式

## 最佳实践

- 将分析结果作为代码审查的参考，而不是自动删除标记为未使用的代码
- 对于标记为未使用但你认为应该保留的代码，考虑添加注释说明其用途
- 定期运行分析器以跟踪项目中的未使用代码
- 结合其他代码质量工具（如SonarQube）使用，获得更全面的代码质量评估

## 自定义和扩展

你可以修改`UnusedCodeAnalyzer.java`文件来适应特定项目的需求：

- 添加更多Spring注解到`isSpringAnnotation`和`isSpringComponentAnnotation`方法
- 修改`isLikelySpringInvokedMethod`方法以包含项目特定的命名约定
- 扩展分析逻辑以考虑其他框架或库的使用模式
