package net.summerfarm;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.NodeList;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.body.Parameter;
import com.github.javaparser.ast.expr.AnnotationExpr;
import com.github.javaparser.ast.expr.MarkerAnnotationExpr;
import com.github.javaparser.ast.expr.MethodCallExpr;
import com.github.javaparser.ast.expr.NameExpr;
import com.github.javaparser.ast.expr.SingleMemberAnnotationExpr;
import com.github.javaparser.ast.type.ClassOrInterfaceType;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;
import com.github.javaparser.resolution.declarations.ResolvedMethodDeclaration;
import com.github.javaparser.symbolsolver.JavaSymbolSolver;
import com.github.javaparser.symbolsolver.resolution.typesolvers.CombinedTypeSolver;
import com.github.javaparser.symbolsolver.resolution.typesolvers.JavaParserTypeSolver;
import com.github.javaparser.symbolsolver.resolution.typesolvers.ReflectionTypeSolver;

/**
 * A Java program to analyze Spring Boot projects and identify unused classes and methods.
 * This analyzer considers Spring Boot specific patterns like annotations, dependency injection,
 * and component scanning.
 */
public class UnusedCodeAnalyzer {

    private static final String PROJECT_PATH = "."; // Current directory
    private static final String SRC_MAIN_PATH = PROJECT_PATH + "/src/main/java";
    private static final String SRC_TEST_PATH = PROJECT_PATH + "/src/test/java";

    // Store all declared classes and methods
    private static final Map<String, ClassInfo> allClasses = new HashMap<>();
    // Store all method calls
    private static final Set<String> calledMethods = new HashSet<>();
    // Store all class usages
    private static final Set<String> usedClasses = new HashSet<>();
    // Store classes and methods only used in tests
    private static final Set<String> testOnlyClasses = new HashSet<>();
    private static final Set<String> testOnlyMethods = new HashSet<>();
    // Store Spring components (classes with Spring annotations)
    private static final Set<String> springComponents = new HashSet<>();
    // Store autowired fields and constructor parameters
    private static final Map<String, Set<String>> autowiredDependencies = new HashMap<>();

    public static void main(String[] args) {
        try {
            // Setup JavaParser with symbol resolution
            setupSymbolSolver();
            
            // First pass: collect all classes and methods
            System.out.println("Collecting all classes and methods...");
            collectAllClassesAndMethods(SRC_MAIN_PATH, false);
            
            // Second pass: identify Spring components and autowired dependencies
            System.out.println("Identifying Spring components and dependencies...");
            identifySpringComponents(SRC_MAIN_PATH);
            
            // Third pass: collect all method calls and class usages in main code
            System.out.println("Analyzing main code usage...");
            analyzeCodeUsage(SRC_MAIN_PATH, false);
            
            // Fourth pass: collect all method calls and class usages in test code
            System.out.println("Analyzing test code usage...");
            analyzeCodeUsage(SRC_TEST_PATH, true);
            
            // Process Spring autowiring (mark autowired components as used)
            System.out.println("Processing Spring autowiring...");
            processSpringAutowiring();
            
            // Find unused classes and methods
            System.out.println("\n=== Classes not used in actual code (excluding test-only) ===");
            findUnusedClasses();
            
            System.out.println("\n=== Methods not used in actual code (excluding test-only) ===");
            findUnusedMethods();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void setupSymbolSolver() {
        CombinedTypeSolver typeSolver = new CombinedTypeSolver();
        typeSolver.add(new ReflectionTypeSolver());
        typeSolver.add(new JavaParserTypeSolver(new File(SRC_MAIN_PATH)));
        
        JavaSymbolSolver symbolSolver = new JavaSymbolSolver(typeSolver);
        // This is the updated line for JavaParser 3.24.4
        com.github.javaparser.StaticJavaParser.getConfiguration().setSymbolResolver(symbolSolver);
    }

    private static void collectAllClassesAndMethods(String sourcePath, boolean isTest) {
        try {
            Files.walk(Paths.get(sourcePath))
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".java"))
                .forEach(path -> {
                    try {
                        ParseResult<CompilationUnit> parseResult = new JavaParser().parse(path.toFile());
                        if (parseResult.isSuccessful() && parseResult.getResult().isPresent()) {
                            CompilationUnit cu = parseResult.getResult().get();
                            
                            // Visit all class declarations
                            cu.findAll(ClassOrInterfaceDeclaration.class).forEach(classDecl -> {
                                String className = getFullyQualifiedName(cu, classDecl);
                                ClassInfo classInfo = new ClassInfo(className, path.toString(), isTest);
                                
                                // Check if class extends or implements other classes/interfaces
                                if (classDecl.getExtendedTypes().isNonEmpty()) {
                                    for (ClassOrInterfaceType extendedType : classDecl.getExtendedTypes()) {
                                        classInfo.addExtendedType(extendedType.getNameAsString());
                                    }
                                }
                                
                                if (classDecl.getImplementedTypes().isNonEmpty()) {
                                    for (ClassOrInterfaceType implementedType : classDecl.getImplementedTypes()) {
                                        classInfo.addImplementedType(implementedType.getNameAsString());
                                    }
                                }
                                
                                allClasses.put(className, classInfo);
                                
                                // Visit all method declarations in this class
                                classDecl.findAll(MethodDeclaration.class).forEach(methodDecl -> {
                                    String methodSignature = className + "." + methodDecl.getSignature().asString();
                                    classInfo.addMethod(methodSignature, isTest);
                                    
                                    // Check for Spring annotations on methods
                                    for (AnnotationExpr annotation : methodDecl.getAnnotations()) {
                                        String annotationName = annotation.getNameAsString();
                                        if (isSpringAnnotation(annotationName)) {
                                            classInfo.addSpringAnnotatedMethod(methodSignature);
                                        }
                                    }
                                });
                            });
                        }
                    } catch (Exception e) {
                        System.err.println("Error parsing file: " + path + " - " + e.getMessage());
                    }
                });
        } catch (IOException e) {
            System.err.println("Error walking directory: " + sourcePath);
        }
    }

    private static void identifySpringComponents(String sourcePath) {
        try {
            Files.walk(Paths.get(sourcePath))
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".java"))
                .forEach(path -> {
                    try {
                        ParseResult<CompilationUnit> parseResult = new JavaParser().parse(path.toFile());
                        if (parseResult.isSuccessful() && parseResult.getResult().isPresent()) {
                            CompilationUnit cu = parseResult.getResult().get();
                            
                            // Check for Spring component annotations on classes
                            cu.findAll(ClassOrInterfaceDeclaration.class).forEach(classDecl -> {
                                String className = getFullyQualifiedName(cu, classDecl);
                                ClassInfo classInfo = allClasses.get(className);
                                
                                if (classInfo != null) {
                                    // Check class annotations
                                    for (AnnotationExpr annotation : classDecl.getAnnotations()) {
                                        String annotationName = annotation.getNameAsString();
                                        if (isSpringComponentAnnotation(annotationName)) {
                                            springComponents.add(className);
                                            classInfo.setSpringComponent(true);
                                            break;
                                        }
                                    }
                                    
                                    // Check for autowired fields and constructor parameters
                                    classDecl.findAll(AnnotationExpr.class).forEach(annotation -> {
                                        if (annotation.getNameAsString().equals("Autowired") || 
                                            annotation.getNameAsString().equals("Resource") ||
                                            annotation.getNameAsString().equals("Inject")) {
                                            
                                            // Get the parent node to determine what's being autowired
                                            annotation.getParentNode().ifPresent(parent -> {
                                                try {
                                                    // Try to resolve the type of the autowired field/parameter
                                                    String autowiredType = resolveAutowiredType(parent);
                                                    if (autowiredType != null) {
                                                        autowiredDependencies.computeIfAbsent(className, k -> new HashSet<>())
                                                                           .add(autowiredType);
                                                    }
                                                } catch (Exception e) {
                                                    // Ignore resolution errors
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    } catch (Exception e) {
                        System.err.println("Error parsing file: " + path + " - " + e.getMessage());
                    }
                });
        } catch (IOException e) {
            System.err.println("Error walking directory: " + sourcePath);
        }
    }

    private static String resolveAutowiredType(Object node) {
        // This is a simplified implementation - in a real analyzer, you would use JavaParser's
        // type resolution capabilities to determine the exact type
        return null; // Placeholder
    }

    private static void analyzeCodeUsage(String sourcePath, boolean isTest) {
        try {
            Files.walk(Paths.get(sourcePath))
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".java"))
                .forEach(path -> {
                    try {
                        ParseResult<CompilationUnit> parseResult = new JavaParser().parse(path.toFile());
                        if (parseResult.isSuccessful() && parseResult.getResult().isPresent()) {
                            CompilationUnit cu = parseResult.getResult().get();
                            
                            // Track class usages
                            cu.findAll(NameExpr.class).forEach(nameExpr -> {
                                try {
                                    String referencedType = nameExpr.calculateResolvedType().describe();
                                    if (allClasses.containsKey(referencedType)) {
                                        if (isTest) {
                                            testOnlyClasses.add(referencedType);
                                        } else {
                                            usedClasses.add(referencedType);
                                        }
                                    }
                                } catch (Exception e) {
                                    // Ignore resolution errors
                                }
                            });
                            
                            // Track method calls
                            cu.findAll(MethodCallExpr.class).forEach(methodCall -> {
                                try {
                                    ResolvedMethodDeclaration resolvedMethod = methodCall.resolve();
                                    String className = resolvedMethod.getClassName();
                                    String methodSignature = className + "." + resolvedMethod.getSignature();
                                    
                                    if (isTest) {
                                        testOnlyMethods.add(methodSignature);
                                    } else {
                                        calledMethods.add(methodSignature);
                                    }
                                } catch (Exception e) {
                                    // Ignore resolution errors
                                }
                            });
                        }
                    } catch (Exception e) {
                        System.err.println("Error parsing file: " + path + " - " + e.getMessage());
                    }
                });
        } catch (IOException e) {
            System.err.println("Error walking directory: " + sourcePath);
        }
    }

    private static void processSpringAutowiring() {
        // Mark all Spring components as used (they're instantiated by Spring)
        for (String componentName : springComponents) {
            usedClasses.add(componentName);
        }
        
        // Mark all autowired dependencies as used
        for (Map.Entry<String, Set<String>> entry : autowiredDependencies.entrySet()) {
            for (String dependency : entry.getValue()) {
                usedClasses.add(dependency);
            }
        }
        
        // Mark all Spring-annotated methods as called (they're invoked by Spring)
        for (ClassInfo classInfo : allClasses.values()) {
            for (String methodSignature : classInfo.getSpringAnnotatedMethods()) {
                calledMethods.add(methodSignature);
            }
        }
    }

    private static boolean isSpringAnnotation(String annotationName) {
        return annotationName.equals("Autowired") ||
               annotationName.equals("RequestMapping") ||
               annotationName.equals("GetMapping") ||
               annotationName.equals("PostMapping") ||
               annotationName.equals("PutMapping") ||
               annotationName.equals("DeleteMapping") ||
               annotationName.equals("PatchMapping") ||
               annotationName.equals("Transactional") ||
               annotationName.equals("Scheduled") ||
               annotationName.equals("EventListener") ||
               annotationName.equals("Bean");
    }
    
    private static boolean isSpringComponentAnnotation(String annotationName) {
        return annotationName.equals("Component") ||
               annotationName.equals("Service") ||
               annotationName.equals("Repository") ||
               annotationName.equals("Controller") ||
               annotationName.equals("RestController") ||
               annotationName.equals("Configuration");
    }

    private static String getFullyQualifiedName(CompilationUnit cu, ClassOrInterfaceDeclaration classDecl) {
        String packageName = cu.getPackageDeclaration().isPresent() ? 
                            cu.getPackageDeclaration().get().getNameAsString() : "";
        return packageName.isEmpty() ? classDecl.getNameAsString() : 
                                     packageName + "." + classDecl.getNameAsString();
    }

    private static void findUnusedClasses() {
        for (Map.Entry<String, ClassInfo> entry : allClasses.entrySet()) {
            String className = entry.getKey();
            ClassInfo classInfo = entry.getValue();
            
            // Skip test classes
            if (classInfo.isTestClass()) {
                continue;
            }
            
            // Skip Spring components (they're used via component scanning)
            if (classInfo.isSpringComponent()) {
                continue;
            }
            
            // Class is unused if it's not in usedClasses and only in testOnlyClasses
            if (!usedClasses.contains(className) && 
                (testOnlyClasses.contains(className) || !testOnlyClasses.contains(className))) {
                System.out.println(className + " - " + classInfo.getFilePath());
            }
        }
    }

    private static void findUnusedMethods() {
        for (Map.Entry<String, ClassInfo> entry : allClasses.entrySet()) {
            String className = entry.getKey();
            ClassInfo classInfo = entry.getValue();
            
            // Skip test classes
            if (classInfo.isTestClass()) {
                continue;
            }
            
            for (String methodSignature : classInfo.getMethods()) {
                // Skip Spring-annotated methods (they're called by Spring)
                if (classInfo.getSpringAnnotatedMethods().contains(methodSignature)) {
                    continue;
                }
                
                // Skip methods in Spring components that look like they might be called by Spring
                if (classInfo.isSpringComponent() && isLikelySpringInvokedMethod(methodSignature)) {
                    continue;
                }
                
                // Method is unused if it's not in calledMethods and only in testOnlyMethods
                if (!calledMethods.contains(methodSignature) && 
                    (testOnlyMethods.contains(methodSignature) || !testOnlyMethods.contains(methodSignature))) {
                    System.out.println(methodSignature + " - " + classInfo.getFilePath());
                }
            }
        }
    }
    
    private static boolean isLikelySpringInvokedMethod(String methodSignature) {
        // Methods that might be called by Spring via reflection or convention
        return methodSignature.contains(".set") || // Setters might be called by Spring
               methodSignature.contains(".get") || // Getters might be called by Spring
               methodSignature.contains(".init") || // Initialization methods
               methodSignature.contains(".afterPropertiesSet") || // InitializingBean method
               methodSignature.contains(".destroy") || // Destruction methods
               methodSignature.contains(".onApplicationEvent"); // Event listeners
    }

    static class ClassInfo {
        private final String className;
        private final String filePath;
        private final boolean isTestClass;
        private final List<String> methods = new ArrayList<>();
        private final Set<String> springAnnotatedMethods = new HashSet<>();
        private final List<String> extendedTypes = new ArrayList<>();
        private final List<String> implementedTypes = new ArrayList<>();
        private boolean isSpringComponent = false;
        
        public ClassInfo(String className, String filePath, boolean isTestClass) {
            this.className = className;
            this.filePath = filePath;
            this.isTestClass = isTestClass;
        }
        
        public void addMethod(String methodSignature, boolean isTestMethod) {
            methods.add(methodSignature);
        }
        
        public void addSpringAnnotatedMethod(String methodSignature) {
            springAnnotatedMethods.add(methodSignature);
        }
        
        public void addExtendedType(String typeName) {
            extendedTypes.add(typeName);
        }
        
        public void addImplementedType(String typeName) {
            implementedTypes.add(typeName);
        }
        
        public void setSpringComponent(boolean isSpringComponent) {
            this.isSpringComponent = isSpringComponent;
        }
        
        public String getClassName() {
            return className;
        }
        
        public String getFilePath() {
            return filePath;
        }
        
        public boolean isTestClass() {
            return isTestClass;
        }
        
        public List<String> getMethods() {
            return methods;
        }
        
        public Set<String> getSpringAnnotatedMethods() {
            return springAnnotatedMethods;
        }
        
        public List<String> getExtendedTypes() {
            return extendedTypes;
        }
        
        public List<String> getImplementedTypes() {
            return implementedTypes;
        }
        
        public boolean isSpringComponent() {
            return isSpringComponent;
        }
    }
}
