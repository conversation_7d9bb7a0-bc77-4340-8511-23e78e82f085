#!/bin/bash

# Spring Boot Unused Code Analyzer Runner Script
# 这个脚本用于构建和运行Spring Boot未使用代码分析器

echo "===== Spring Boot Unused Code Analyzer ====="
echo "构建分析器..."

# 创建临时目录
TEMP_DIR="temp_analyzer"
mkdir -p $TEMP_DIR

# 复制分析器文件到临时目录
JAVA_SOURCE_DIR=$TEMP_DIR/src/main/java/net/summerfarm/
mkdir -p $JAVA_SOURCE_DIR
cp UnusedCodeAnalyzer.java $JAVA_SOURCE_DIR/
cp analyzer-pom.xml $TEMP_DIR/pom.xml

# 进入临时目录并构建分析器
cd $TEMP_DIR
mvn clean package -q

if [ $? -ne 0 ]; then
    echo "构建失败，请检查Maven配置和依赖项。"
    cd ..
    exit 1
fi

# 复制生成的JAR文件到项目根目录
echo "$(ls -lah target/)"
# cp target/code-analyzer-1.0.0-jar-with-dependencies.jar ..
# cd ..



echo "分析器构建完成。"
echo "开始分析项目..."

# 运行分析器并将结果保存到文件
cd ..
java -jar $TEMP_DIR/target/code-analyzer-1.0.0-jar-with-dependencies.jar > unused-code-report.txt

echo "分析完成！结果已保存到 unused-code-report.txt"
echo "你可以使用以下命令查看结果："
echo "cat unused-code-report.txt"
# 删除临时目录
rm -rf $TEMP_DIR