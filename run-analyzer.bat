@echo off
REM Spring Boot Unused Code Analyzer Runner Script
REM 这个脚本用于构建和运行Spring Boot未使用代码分析器

echo ===== Spring Boot Unused Code Analyzer =====
echo 构建分析器...

REM 创建临时目录
set TEMP_DIR=temp_analyzer
mkdir %TEMP_DIR%

REM 复制分析器文件到临时目录
copy UnusedCodeAnalyzer.java %TEMP_DIR%\
copy analyzer-pom.xml %TEMP_DIR%\pom.xml

REM 进入临时目录并构建分析器
cd %TEMP_DIR%
call mvn clean package -q

if %ERRORLEVEL% neq 0 (
    echo 构建失败，请检查Maven配置和依赖项。
    cd ..
    exit /b 1
)

REM 复制生成的JAR文件到项目根目录
copy target\code-analyzer-1.0.0-jar-with-dependencies.jar ..
cd ..

REM 删除临时目录
rmdir /s /q %TEMP_DIR%

echo 分析器构建完成。
echo 开始分析项目...

REM 运行分析器并将结果保存到文件
java -jar code-analyzer-1.0.0-jar-with-dependencies.jar > unused-code-report.txt

echo 分析完成！结果已保存到 unused-code-report.txt
echo 你可以使用以下命令查看结果：
echo type unused-code-report.txt
