package net.summerfarm.persistence;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * @Package: net.summerfarm.mall.persistence
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/5/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantRepositoryTest {
//    @Resource
//    private MerchantRepository merchantRepository;
//
//    @Test
//    public void findByOpenid() throws Exception {
//        ObjectMapper mapper = new ObjectMapper();
//        System.out.println(mapper.writeValueAsString(merchantRepository.findByOpenid("oBTbNwTavnUiKaCG8Ut7A")));
//    }

}