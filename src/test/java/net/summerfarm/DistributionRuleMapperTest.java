package net.summerfarm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.summerfarm.model.dto.SummerfarmDeliveryFeeRuleDTO;
import net.summerfarm.mall.MallApplication;
import net.summerfarm.mall.common.util.WeChatUtils;
import net.summerfarm.mall.contexts.BrokenAfterSaleOrder;
import net.summerfarm.mall.contexts.BrokenCalculator;
import net.summerfarm.mall.contexts.ExchangeGoodsAfterSaleOrder;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DistributionRuleVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.OrderItemVO;
import net.summerfarm.mall.service.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2019/7/8  3:11 PM
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = MallApplication.class)
//@ContextConfiguration
public class DistributionRuleMapperTest {

    @Resource
    private DistributionRuleMapper distributionRuleMapper;

    @Resource
    private DistributionRuleService distributionRuleService;
    @Resource
    private OrderService orderService;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    DeliveryPlanService deliveryPlanService;
    @Resource
    OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Resource
    AfterSaleCalculatorFactory afterSaleCalculatorFactory;
    @Resource
    BrokenAfterSaleOrder brokenAfterSaleOrder;
    @Resource
    OrderItemMapper orderItemMapper;
    @Resource
    MerchantGroupPurchaseMapper merchantGroupPurchaseMapper;
    @Resource
    TrolleyService trolleyService;
    @Resource
    PrepayInventoryService prepayInventoryService;
    @Resource
    BrokenCalculator brokenCalculator;
    @Resource
    ExchangeGoodsAfterSaleOrder exchangeGoodsAfterSaleOrder;

    @Test
    public void  sf(){

        DistributionRuleVO as = distributionRuleMapper.queryByAdminId(275, 1001);
        System.out.println(as.getDeliveryFee());
    }

//    @Test
//    public void af(){
//        Map<String, String> map = afterSaleOrderService.calcAfterSaleCoupon("02156877719962847", "2220427278", 3, 0, 0, 0, null);
//        System.out.println(map.get("couponMoney"));
//    }

    @Test
    public void afterSale(){
        AfterSaleOrderVO afterSaleOrder = new AfterSaleOrderVO();
        afterSaleOrder.setRefundType("拍多/拍错");
        afterSaleOrder.setQuantity(7);
        afterSaleOrder.setOrderNo("01162273108329853");
        afterSaleOrder.setDeliveryed(0);
        afterSaleOrder.setType(0);
        afterSaleOrder.setSku("2220427278");
        afterSaleOrder.setSuitId(0);
        afterSaleOrder.setDeliveryId(null);
        try{
            afterSaleOrderService.save(afterSaleOrder);
        } catch (Exception e){
            System.out.println(e.getMessage());


        }

    }

    @Test
    public void refund(){
        try{
            AfterSaleOrderVO  afterSaleOrderVO =  afterSaleOrderMapper.selectByAfterSaleOrderNo("78031420021");
            System.out.println(afterSaleOrderVO.getAfterSaleType());
        }catch (Exception e){

        }

    }

    @Test
    public void sdf(){
        String weChatCode = WeChatUtils.getWeChatCode("/home.html#/loading?name=selfOrderCanAfterSale");
        System.out.println(weChatCode);
    }
    @Test
    public void sfsd(){
        deliveryPlanService.orderDeliveryNotice();
    }

    @Test
    public void trolleyPrice(){
//        OrderItemVO orderItemVO = new OrderItemVO();
//        orderItemVO.setSku("6554523205");
//        orderItemVO.setQuantity(3);
//        orderItemVO.setValidFlag(true);
//        orderItemVO.setPrice(BigDecimal.valueOf(127));
//        List<OrderItemVO> orderItemVOS = new ArrayList<>();
//        orderItemVOS.add(orderItemVO);
//        MerchantSubject merchantSubject = new MerchantSubject();
//        merchantSubject.setAdminId(279);
//        merchantSubject.setDirect(1);
//        merchantSubject.setSize("大客户");
//        trolleyService.trolleyPrice(orderItemVOS,merchantSubject,true);
    }

    @Test
    public  void df(){
        String ss = "{\"orderNo\":\"04161122043426856\",\"sku\":\"5411764638\",\"afterSaleUnit\":\"kg\",\"handleRemark\":\"\",\"handleType\":7,\"proofPic\":\"\",\"quantity\":1,\"handleNum\":0,\"suitId\":0,\"deliveryed\":1,\"recoveryType\":0,\"recoveryNum\":0,\"afterSaleType\":\"包装问题\"}";
        AfterSaleOrderVO vo = JSONObject.parseObject(ss, AfterSaleOrderVO.class);
        exchangeGoodsAfterSaleOrder.afterSaleOrder(vo);
    }

    @Test
    public void testDeliveryFee(){
        String deliveryFeeRule = "{\"categoryList\":[186],\"totalPrice\":500,\"fruitPrice\":150,\"freeDeliveryWeek\":\"2,3\"}";
        SummerfarmDeliveryFeeRuleDTO summerfarmDeliveryFeeRuleDTO = JSON.parseObject(deliveryFeeRule, SummerfarmDeliveryFeeRuleDTO.class);
        Assert.assertNotNull(summerfarmDeliveryFeeRuleDTO);
        System.out.println(summerfarmDeliveryFeeRuleDTO);
    }
}
