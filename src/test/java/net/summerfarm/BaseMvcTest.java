package net.summerfarm;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.MD5Util;
import net.summerfarm.mall.MallApplication;
import net.summerfarm.mall.common.filter.ReplacePriceFilter;
import net.summerfarm.mall.mapper.MerchantSubAccountMapper;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.vo.LoginResponseVO;
import net.summerfarm.mall.service.MerchantService;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MallApplication.class)
@ActiveProfiles("dev2")
public abstract class BaseMvcTest {
    @Autowired
    protected WebApplicationContext webApplicationContext;
    @Resource
    private MerchantService merchantService;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;

    protected MockMvc mvc;

    private String token;

    @Before
    public void login() {
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).addFilters(new ReplacePriceFilter()).build();

        MerchantSubAccount account = merchantSubAccountMapper.selectMangerByMId(loginMId());
        String deStr = account.getPhone() + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "login";
        AjaxResult login = merchantService.login(null, null, account.getPhone(), MD5Util.string2MD5(deStr));
        token = ((LoginResponseVO)login.getData()).getToken();
    }

    public abstract Long loginMId();

    protected MvcResult postTest(String url, MultiValueMap<String, String> params) throws Exception {
        return doTest(url, RequestMethod.POST, params, "", MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult postTest(String url, String jsonData) throws Exception {
        return doTest(url, RequestMethod.POST, null, jsonData, MediaType.APPLICATION_JSON);
    }

    protected MvcResult getTest(String url) throws Exception {
        return doTest(url, RequestMethod.GET, null, "", MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult getTest(String url, MultiValueMap<String, String> params) throws Exception {
        return doTest(url, RequestMethod.GET, params, "", MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult doTest(String url, RequestMethod requestMethod, MultiValueMap<String, String> params, String jsonData, MediaType mediaType) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            params = new LinkedMultiValueMap<>();
        }
        if (jsonData == null) {
            jsonData = "";
        }
        MockHttpServletRequestBuilder builder = null;
        switch (requestMethod) {
            case POST:
                builder = MockMvcRequestBuilders.post(url);
                break;
            case PUT:
                builder = MockMvcRequestBuilders.put(url);
                break;
            case DELETE:
                builder = MockMvcRequestBuilders.delete(url);
                break;
            default:
                builder = MockMvcRequestBuilders.get(url);
                break;
        }


        return mvc.perform(
                        builder.accept(MediaType.APPLICATION_JSON)
                                .params(params)
                                .contentType(mediaType)
                                .content(jsonData)
                                .header("token", token)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
