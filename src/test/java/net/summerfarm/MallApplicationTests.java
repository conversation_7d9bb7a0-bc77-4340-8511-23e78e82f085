package net.summerfarm;

import net.summerfarm.mall.contexts.Global;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MallApplicationTests {

	private MockMvc mvc;

	@Resource
	private WebApplicationContext webApplicationContext;

	@Before
	public void setUp() throws Exception {
//		mvc = MockMvcBuilders.standaloneSetup(new DemoController()).build();
		mvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
	}

	@Test
	public void contextLoads() throws Exception {
		mvc.perform(MockMvcRequestBuilders.get("/foo").accept(MediaType.APPLICATION_JSON).sessionAttr(Global.MERCHANT_ID, 2))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andDo(MockMvcResultHandlers.print())
				.andReturn();
	}

	@Test
	public void merchanCoupon() throws Exception {
		mvc.perform(MockMvcRequestBuilders.get("/activity/mycoupon").accept(MediaType.APPLICATION_JSON).sessionAttr(Global.MERCHANT_ID, 2))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andDo(MockMvcResultHandlers.print())
				.andReturn();
	}

	@Autowired
	private RedisTemplate redisTemplate;

	@Test
	public void test() throws Exception {

		// 保存字符串
        redisTemplate.opsForValue().set("aaa", "111");
		Assert.assertEquals("111", redisTemplate.opsForValue().get("aaa"));

	}
}
