package net.summerfarm.mall.mapper;

import net.summerfarm.mall.MallApplication;
import net.summerfarm.mall.model.vo.ProductsPropertyValueVO;
import net.summerfarm.mall.service.ProductsPropertyValueService;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = MallApplication.class)
@ActiveProfiles("dev2")
@RunWith(SpringRunner.class)
@Slf4j
public class ProductsPropertyValueServiceTest {

    @Autowired
    private ProductsPropertyValueMapper productsPropertyValueMapper;

    @Autowired
    private ProductsPropertyValueService productsPropertyValueService;

    @Test
    public void testSelectByPdIds() {
        List<Long> pdIds = Arrays.asList(41286L, 41285L, 41284L);
        List<ProductsPropertyValueVO> result = productsPropertyValueMapper.selectByPdIds(pdIds);
        log.info("result:{}", result);
        assertNotNull(result);
        assertFalse(result.isEmpty());

        Map<Long, List<ProductsPropertyValueVO>> mapResult = productsPropertyValueService
                .getProductsPropertyValueCache(pdIds);
        log.info("mapResult:{}", mapResult);
        assertNotNull(result);
        assertFalse(result.isEmpty());

        productsPropertyValueService.getProductsPropertyValueCache(pdIds);
        productsPropertyValueService.getProductsPropertyValueCache(Arrays.asList(41286L, 41285L, 41284L, 444L));
    }
}
