package net.summerfarm.mall.mapper;

import com.alibaba.fastjson.JSON;
import net.summerfarm.mall.model.domain.DeliveryEvaluation;
import net.summerfarm.mall.service.cache.DeliveryEvaluationCache;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: zhuyantao
 * @date: 2023/3/17 3:20 下午
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DeliveryElMapperTest {

    @Resource
    DeliveryEvaluationMapper deliveryEvaluationMapper;

    @Rollback(false)
    @Test
    public void test1(){
        DeliveryEvaluation el = new DeliveryEvaluation();
        el.setDeliveryPlanId(1234324325);
        List<DeliveryEvaluation> el1 = deliveryEvaluationMapper.selectList(el);
        System.out.println(JSON.toJSONString(el1));
    }
}
