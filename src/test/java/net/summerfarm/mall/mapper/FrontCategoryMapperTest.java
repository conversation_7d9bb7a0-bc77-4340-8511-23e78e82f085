package net.summerfarm.mall.mapper;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.model.vo.FrontCategoryVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles("dev2")
public class FrontCategoryMapperTest {

    @Resource
    private FrontCategoryMapper frontCategoryMapper;

    @Test
    public void testSelectByParentId() {
        List<FrontCategoryVO> frontCategoryVOList = frontCategoryMapper.selectParentFrontCategory(1001);
        Assert.assertFalse(CollectionUtils.isEmpty(frontCategoryVOList));
        frontCategoryVOList.forEach(frontCategoryVO -> {
            log.info("前端类目:{}", frontCategoryVO);
        });
    }
}
