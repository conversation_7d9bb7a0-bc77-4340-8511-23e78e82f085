package net.summerfarm.mall.common.sms.imp;

import com.alibaba.fastjson.JSON;
import net.summerfarm.enums.SMSType;
import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.mapper.SMSSceneMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.Config;
import net.summerfarm.mall.model.domain.SMSScene;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

import static net.summerfarm.mall.common.sms.imp.ChuangLanSMSSender.ACCOUNT_KEY;
import static net.summerfarm.mall.common.sms.imp.ChuangLanSMSSender.PASSWORD_KEY;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ChuangLanSMSSenderTest {
    @Resource
    private ChuangLanSMSSender chuangLanSMSSender;

    @Test
    public void render() {

        ChuangLanSMSSender chuangLanSMSSender = new ChuangLanSMSSender();
        SMSSceneMapper smsSceneMapper = Mockito.mock(SMSSceneMapper.class);

        SMSScene smsScene = new SMSScene();
        smsScene.setTemplate("{s},你好，你的采购单{s}有{s}入库保质期和采购单不符，请查看，并确认收货");
        Mockito.when(smsSceneMapper.selectByScenePlatform(Mockito.eq(3L), Mockito.eq(3)))
                .thenReturn(smsScene);
        chuangLanSMSSender.setSmsSceneMapper(smsSceneMapper);


        // 测试渲染模板
        String res = chuangLanSMSSender.renderTemp(3L, Arrays.asList("小明", "1234", "3"), "111");
        Assert.assertEquals("小明,你好，你的采购单1234有3入库保质期和采购单不符，请查看，并确认收货", res);

        ConfigMapper configMapper = Mockito.mock(ConfigMapper.class);
        chuangLanSMSSender.setConfigMapper(configMapper);

        Config accountConfig = new Config();
        accountConfig.setValue("N5660970");
        Config passwordConfig = new Config();
        passwordConfig.setValue("Q15PJu7U7U59e4");
        Mockito.when(configMapper.selectByKey(Mockito.eq(ACCOUNT_KEY))).thenReturn(accountConfig);
        Mockito.when(configMapper.selectByKey(Mockito.eq(PASSWORD_KEY))).thenReturn(passwordConfig);

        SMS sms = new SMS();
        sms.setPhone("***********");
        sms.setSceneId(3L);
        sms.setArgs(Arrays.asList("小明", "1234", "3"));
        //测试发送短信
        boolean success = chuangLanSMSSender.sendSMS(sms);
        Assert.assertTrue(success);
    }

    @Test
    public void sendSMS() {
        SMS sms = new SMS();
        sms.setPhone("***********");
        sms.setContent("【鲜沐农场】尊敬的用户，你的验证码是：123456，请在10分钟内输入。请勿告诉其他人。");

        ChuangLanSMSSender sender = new ChuangLanSMSSender();
        ConfigMapper configMapper = Mockito.mock(ConfigMapper.class);
        sender.setConfigMapper(configMapper);

        Config accountConfig = new Config();
        accountConfig.setValue("N5660970");
        Config passwordConfig = new Config();
        passwordConfig.setValue("Q15PJu7U7U59e4");
        Mockito.when(configMapper.selectByKey(Mockito.eq(ACCOUNT_KEY))).thenReturn(accountConfig);
        Mockito.when(configMapper.selectByKey(Mockito.eq(PASSWORD_KEY))).thenReturn(passwordConfig);

        boolean res = sender.sendSMS(sms);
        Assert.assertTrue(res);

        accountConfig.setValue("M5976181");
        passwordConfig.setValue("o236KjY5cYbb22");
        sms.setContent("尊敬的用户，参与问卷调查得10元12123券，点击https://wjx.cn/vj/eoX5Vw3.aspx");
        sender.sendSMS(sms);

    }

    @Test
    public void decode() {
        String s = "{\"args\":[\"122\", \"134\"], \"phone\":\"***********\",\"type\":1}";
        SMS sms = JSON.parseObject(s, SMS.class);
        Assert.assertEquals(SMSType.MARKET, sms.getType());

        s = "{\"args\":[\"122\", \"134\"], \"phone\":\"***********\",\"type\":2}";
        sms = JSON.parseObject(s, SMS.class);
        Assert.assertEquals(SMSType.NOTIFY, sms.getType());

    }
}