package net.summerfarm.mall.common.sms.imp;

import net.summerfarm.mall.common.sms.SMSSender;
import net.summerfarm.mall.model.SMS;
import org.junit.Test;

public class DingdongSMSSenderTest {

    @Test
    public void sendSMS() {
        SMSSender sender = new DingdongSMSSender();
        SMS sms = new SMS();
        sms.setContent("鲜沐农场】尊敬的用户，你的验证码是： ，请在10分钟内输入。请勿告诉其他人。");
        sms.setPhone("15779347398");
        sender.sendSMS(sms);
    }

}