package net.summerfarm.mall.common.sms.imp;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.mall.mapper.SMSSceneMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.SMSScene;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.Arrays;

public class AliSMSSenderTest {

    @Test
    public void sendSMS() {
        AliSMSSender smsSender = new AliSMSSender();
        SMSSceneMapper smsSceneMapper = Mockito.mock(SMSSceneMapper.class);

        SMSScene smsScene = new SMSScene();
        smsScene.setTemplateId("SMS_152287112");
        Mockito.when(smsSceneMapper.selectByScenePlatform(Mockito.eq(1L), Mockito.eq(1)))
                        .thenReturn(smsScene);
        smsSender.setSmsSceneMapper(smsSceneMapper);

        SMS sms = new SMS();
        sms.setArgs(Arrays.asList("1", "2", "3", "4"));
        sms.setSceneId(1L);
        sms.setPhone("15779347398");
        //发短信
        boolean res = smsSender.sendSMS(sms);
        Assert.assertTrue(res);
    }

    @Test
    public void testParseArgs(){
        JSONObject res = AliSMSSender.parseArgs(Arrays.asList("123", "456", "789"));
        Assert.assertEquals("123", res.getString("one"));
        Assert.assertEquals("456", res.getString("two"));
        Assert.assertEquals("789", res.getString("three"));
    }
}