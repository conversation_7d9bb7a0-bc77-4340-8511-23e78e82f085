package net.summerfarm.mall.common.sms.imp;

import net.summerfarm.mall.model.SMS;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@SpringBootTest
@RunWith(SpringRunner.class)
public class SMSSenderTest {
    @Resource
    private ChuangLanSMSSender chuangLanSMSSender;
    @Resource
    private AliSMSSender aliSMSSender;

    @Test
    public void testCL() {
        SMS sms = new SMS();
        sms.setPhone("15779347398");
        sms.setSceneId(1L);
        sms.setArgs(Arrays.asList("123456"));
        boolean success = chuangLanSMSSender.sendSMS(sms);
        Assert.assertTrue(success);
    }

    @Test
    public void testAli(){
        SMS sms = new SMS();
        sms.setPhone("15779347398");
        sms.setSceneId(1L);
        sms.setArgs(Arrays.asList("123456"));
        boolean success = aliSMSSender.sendSMS(sms);
        Assert.assertTrue(success);
    }

}
