package net.summerfarm.mall.common.sms;

import net.summerfarm.mall.common.sms.imp.AliSMSSender;
import net.summerfarm.mall.common.sms.imp.ChuangLanSMSSender;
import net.summerfarm.mall.common.sms.imp.DingdongSMSSender;
import net.summerfarm.mall.common.sms.imp.LocalSMSSender;
import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.Config;
import net.summerfarm.mall.service.ConfigService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class SMSSenderFactoryTest {

    @Test
    public void getSMSSender() {
        SMSSenderFactory context = new SMSSenderFactory();
        context.setAliSMSSender(new AliSMSSender());
        context.setDingdongSMSSender(new DingdongSMSSender());
        context.setChuangLanSMSSender(new ChuangLanSMSSender());
        context.setLocalSMSSender(new LocalSMSSender());
        ConfigService configMapper = Mockito.mock(ConfigService.class);
        context.setConfigService(configMapper);

        Config config = new Config();
        config.setValue("1");
        //Mockito.when(configMapper.getValue(Mockito.anyString())).thenReturn(config);
        SMSSender smsSender = context.getSMSSender();
        Assert.assertEquals(smsSender.getClass(), AliSMSSender.class);

        config.setValue("2");
        smsSender = context.getSMSSender();
        Assert.assertEquals(smsSender.getClass(), DingdongSMSSender.class);

        config.setValue("3");
        smsSender = context.getSMSSender();
        Assert.assertEquals(smsSender.getClass(), ChuangLanSMSSender.class);

        config.setValue("0");
        smsSender = context.getSMSSender();
        Assert.assertEquals(smsSender.getClass(), LocalSMSSender.class);
        smsSender.sendSMS(new SMS());

    }

}