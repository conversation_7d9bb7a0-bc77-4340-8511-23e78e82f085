package net.summerfarm.mall;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.MerchantSubAccountMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.ProductRecommendService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-09-16
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProductsRecommendTest {
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    //redis key
    private static final String MID = "product_recommend_mid";
    private static final String SKU = "product_recommend_sku";

    @Test
    public void testData(){
        String[] skuArr = new String[]{"6517064563",
                "6517064608",
                "6517064386",
                "5416383435",
                "5483773238",
                "5488747425",
                "5488758465",
                "5462856110",
                "5468355701",
                "25271082087",
                "5428570184",
                "5408183437",
                "5438487358",
                "5470454622",
                "5470454455",
                "19201076164",
                "5485273543",
                "5487748022",
                "24381074281",
                "5474578153",
                "5412262417",
                "5446286460",
                "5430586807",
                "4886640653",
                "5414266117",
                "25207505440",
                "5414266230",
                "5414266851",
                "18855161067",
                "5454531665",
                "5450556462",
                "25142301123",
                "5466723175",
                "5466723162",
                "5414266771",
                "5472085843",
                "7142054048",
                "5433057204",
                "24244657355",
                "4803870570",
                "4828662424",
                "4817545237",
                "5461524378",
                "5408700871",
                "5452151857",
                "5452151322",
                "5457328841",
                "4810501763",
                "4810501253",
                "5450251810",
                "5444717480",
                "4881737466",
                "5464062131",
                "5440112870",
                "4877278176",
                "5471274211",
                "5470608464",
                "5488620832",
                "5470608806",
                "5454250306",
                "6356652420",
                "5464062053",
                "4831436200",
                "5414188760",
                "5414188522",
                "6356652574",
                "4831436721",
                "5441008468",
                "5027780575",
                "5400458378",
                "19335603756",
                "18384480155",
                "2274182871",
                "6316408561",
                "4815076838",
                "6370520146",
                "5473816848",
                "5400771063",
                "5470832150",
                "5416513132",
        };
        String[] mIdArr = new String[]{"818",
                "77",
                "879",
                "2",
                "878",
                "877",
                "772",
                "876",
                "821",
                "763",
                "84",
                "875",
                "873",
                "19",
                "70",
                "874",
                "83",
                "872",
                "871",
                "870",
                "869",
                "868",
                "867",
                "866",
                "864",
                "865",
                "863",
                "862",
                "854",
                "855",
                "820",
                "819",
                "766",
                "759",
                "758",
                "756",
                "764",
                "752",
                "28",
                "751",
                "769",
                "765",
                "731",
                "707",
                "746",
                "762",
                "750",
                "749",
                "748",
                "739",
                "734",
                "16",
                "729",
                "727",
                "726",
                "720",
                "719",
                "718",
                "711",
                "705",
                "664",
                "670",
                "85",
                "71",
                "669",
                "667",
                "60",
                "761",
                "760",
                "90",
                "86",
                "61",
                "29",
                "82",
                "68",
                "7",
                "66",
                "38"
        };
        //u2i
        for (String mId : mIdArr) {
            randomSku(skuArr).forEach(el -> addToRedis(true, mId, el));
        }
        addToRedis(true, "123", "11232");
        //i2u
        for (String sku : skuArr) {
            randomSku(skuArr).forEach(el -> addToRedis(false, sku, el));
        }
        addToRedis(true, "123", "11232");
    }

    List<String> randomSku(String[] skuArr){
        Set<String> result = new HashSet<>();

        Random random = new Random();
        while (result.size() < 20){
            result.add(skuArr[random.nextInt(skuArr.length)]);
        }
        return new ArrayList<>(result);
    }

    private String listKey = null;
    private final List<String> list = new ArrayList<>();

    private void addToRedis(boolean u2i, String key, String sku) {
        if (listKey != null && !Objects.equals(listKey, key)) {
            redisTemplate.opsForHash().delete(u2i ? MID : SKU, listKey);
            redisTemplate.opsForHash().put(u2i ? MID : SKU, listKey, list);
            list.clear();
        }

        listKey = key;
        list.add(sku);
    }

    @Resource
    private ProductRecommendService productRecommendService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private OrderService orderService;
    @Test
    public void ttt(){
        // productRecommendService.syncRecommendData(); 
    }
}
