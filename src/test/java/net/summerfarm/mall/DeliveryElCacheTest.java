package net.summerfarm.mall;

import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.service.cache.DeliveryEvaluationCache;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: zhuyantao
 * @date: 2023/3/17 3:20 下午
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
//@SpringBootTest(classes={DeliveryEvaluationCache.class, ConfigMapper.class})
public class DeliveryElCacheTest {

    @Resource
    DeliveryEvaluationCache deliveryEvaluationCache;

    @Rollback(false)
    @Test
    public void test1(){
        Boolean bool = deliveryEvaluationCache.getLimitAreaNoFlag("DELIVERY_EL_LIMIT_AREA_FLAG");
        List<Integer> list = deliveryEvaluationCache.getLimitAreaNoList("DELIVERY_EL_LIMIT_AREA_LIST");
        Long l = deliveryEvaluationCache.getLimitAreaNoDays("DELIVERY_EL_LIMIT_TIME");
        System.out.println();
    }
}
