package net.summerfarm.mall;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.mall.client.req.OrderReq;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.contexts.NotNeedCalculator;
import net.summerfarm.mall.mapper.AdminMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.domain.Admin;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.provider.impl.OrderProviderImpl;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.FenceService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.ofc.OfcDeliveryService;
import net.summerfarm.mall.service.popmall.PopOrderRelationService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-01-20
 * @description
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderServiceTest {
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private OrderService orderService;

    @Resource
    private NotNeedCalculator notNeedCalculator;

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    FenceService fenceService;
    @Resource
    private OfcDeliveryService ofcDeliveryService;

    @Resource
    private PopOrderRelationService popOrderRelationService;

    @Resource
    private OrderProviderImpl orderProvider;

    @Test
    public void placeOrderTest() {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setOrderNo("01161639813718982");
        afterSaleOrderVO.setQuantity(2);
        afterSaleOrderVO.setSku("**********");
        notNeedCalculator.calcRefund(afterSaleOrderVO);

    }

    @Test
    public void testOrderCountByMid() {
        Integer count = ordersMapper.countOrderByDate(1234L, LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalDate.now());
        log.info("count:{}", count);
        count = ordersMapper.countOrderByDate(349375L, LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalDate.now());
        log.info("count:{}", count);
    }

    @Test
    public void deliveryDate() {
        Admin admin = adminMapper.selectByPrimaryKey(1);
        System.out.println("111111111" + admin.getCloseOrderTime());
    }

    @Test
    public void find() {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setAfterSaleOrderNo("853232060026");
        afterSaleOrderVO.setHandleNum(BigDecimal.ZERO);
        afterSaleOrderVO.setQuantity(2);
        afterSaleOrderVO.setHandleType(7);
        afterSaleOrderVO.setSku("29633835226");
        afterSaleOrderVO.setOrderNo("01162342275076229");
        afterSaleOrderService.handle(afterSaleOrderVO);
    }

    @Resource
    private MqProducer mqProducer;

    @Test
    public void testMQ() {
        MQData mqData = new MQData();
        mqData.setType(MType.flush.name());
        mqData.setData("123456");
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST, null, JSONObject.toJSONString(mqData));
    }

    @Test
    public void testOrder() {
        Contact contact = new Contact();
        contact.setCity("杭州市");
        contact.setArea("余杭区");
        fenceService.checkFenceStatus(contact);
    }

    @Test
    public void testDetail() {
        orderService.orderDetails("01167927986181183");
    }

    @Test
    public void stockOutWeChatMessage() {
        MQData mqData = new MQData();
        JSONObject map = new JSONObject();
        map.put("orderNo", "PO25AM0VTR0102145243");
        map.put("shortSkuName", "beitaPOP商品西瓜");
        map.put("shortSkuAmount", "1件");
        map.put("shortSku", "2176873037771");
        mqData.setData(map);
        ofcDeliveryService.stockOutWeChatMessage(mqData);
    }

    @Test
    public void testPopOrderRelation() {
        String sourcePopOrderNo = "PO25SCOR1N0427164376";
        String popOrderNo = popOrderRelationService.findHelpOrderByPopOrderNo(sourcePopOrderNo);
        System.out.println(popOrderNo);
    }

    public void testCloseOrder() {
        OrderReq orderReq = new OrderReq();
        orderReq.setOrderNo("0425CLJEHQ0427164308");
        orderProvider.closeOrder(orderReq);
    }

}
