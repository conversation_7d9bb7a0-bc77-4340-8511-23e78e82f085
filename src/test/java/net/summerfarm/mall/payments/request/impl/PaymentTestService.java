package net.summerfarm.mall.payments.request.impl;

import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.Refund;
import net.summerfarm.mall.model.input.payment.PaymentInput;
import net.summerfarm.mall.payments.request.PaymentHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-06-20
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("qa")
public class PaymentTestService {

    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private RefundMapper refundMapper;

    @Test
    public void testPayV2() {
        String masterOrderNo = "M01172017621887065";
        Integer channel = 0;
        PaymentInput input = new PaymentInput();
        input.setMasterOrderNo(masterOrderNo);
        input.setPayChannel(channel);
        paymentHandler.payV2(input);
    }

    @Test
    public void testQueryPaymentResult() {
        String masterOrderNo = "M01171955900586401";
        paymentHandler.syncPaymentResultV2(masterOrderNo);
    }

    @Test
    public void testClosePayment() {
        String masterOrderNo = "M01171886221585836";
        paymentHandler.closeOrderV2(masterOrderNo);
    }

    @Test
    public void testRefund() {
        Refund refund = refundMapper.selectByRefundNo("01257IEAWX0219174455YRE0XD");
        paymentHandler.performRefund(refund);
    }

    @Test
    public void testQueryRefundResult() {
        String refundNo = "01257IEAWX0219174455YRE0XD";
        paymentHandler.queryRefund(refundNo);
    }
}
