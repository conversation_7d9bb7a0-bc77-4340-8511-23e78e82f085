package net.summerfarm.mall.provider.impl;

import net.summerfarm.mall.client.provider.DeliveryPlanProvider;
import net.summerfarm.mall.client.req.DeliveryPlanUpdateStoreReq;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 *
 *
 * @author: xiaowk
 * @date: 2024/8/5 下午2:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class DeliveryPlanProviderImplTest {

    @Resource
    private DeliveryPlanProvider deliveryPlanProvider;

    @Test
    void updateDeliveryPlanStore() {
        DeliveryPlanUpdateStoreReq deliveryPlanUpdateStoreReq = new DeliveryPlanUpdateStoreReq();
        deliveryPlanUpdateStoreReq.setOrderNo("02172223258980274");
        deliveryPlanUpdateStoreReq.setDeliveryTime(LocalDate.parse("2024-07-31"));
        deliveryPlanUpdateStoreReq.setContactId(341231L);
        deliveryPlanUpdateStoreReq.setOldStoreNo(204);
        deliveryPlanUpdateStoreReq.setNewStoreNo(208);
        deliveryPlanProvider.updateDeliveryPlanStore(deliveryPlanUpdateStoreReq);
    }
}