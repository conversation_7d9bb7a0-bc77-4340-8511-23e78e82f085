package net.summerfarm.mall.tasks;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.MallApplication;
import net.summerfarm.mall.task.weixinmp.CreateWeixinMpMenuTask;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest(classes = MallApplication.class)
@ActiveProfiles("qa")
@RunWith(SpringRunner.class)
@Slf4j
public class CreateWeixinMpMenuTaskTest {

    @Resource
    private CreateWeixinMpMenuTask createWeixinMpMenuTask;

    @Test
    public void testCreateMenu() throws Exception {
        String testJson = "{\"button\":[{\"name\":\"鲜沐QA\",\"url\":\"/home.html\",\"type\":\"view\"},{\"name\":\"客服QA\",\"url\":\"/home.html#/loading?name=selfProblem\",\"type\":\"view\"},{\"name\":\"常用\",\"sub_button\":[{\"name\":\"配方集市\",\"url\":\"/home.html?name=new-meetings&id=2342&isMobile=true\",\"type\":\"view\"},{\"name\":\"鲜沐之家\",\"url\":\"https://www.summerfarm.net\",\"type\":\"view\"},{\"name\":\"发起售后\",\"url\":\"/home.html#/loading?name=selfOrderCanAfterSale\",\"type\":\"view\"},{\"name\":\"我的订单\",\"url\":\"/home.html#/loading?name=selfOrder\",\"type\":\"view\"}]}]}";
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setJobParameters(testJson);
        ProcessResult processResult = createWeixinMpMenuTask.processResult(xmJobInput);
        log.info("processResult: {}", processResult);
        Assert.assertTrue(processResult != null);
        Assert.assertTrue(processResult.getStatus() == InstanceStatus.SUCCESS);
    }
}
