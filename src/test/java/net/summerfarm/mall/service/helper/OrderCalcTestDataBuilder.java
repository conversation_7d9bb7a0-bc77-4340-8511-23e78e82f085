package net.summerfarm.mall.service.helper;

import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.model.domain.Trolley;
import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单计算测试数据构建器
 * 
 * <AUTHOR>
 */
public class OrderCalcTestDataBuilder {

    /**
     * 创建基础的PlaceOrderVO用于测试
     */
    public static PlaceOrderVO createBasicPlaceOrderVO() {
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setTakePriceFlag(true); // 获取明细，不均摊优惠
        placeOrderVO.setIsTakePrice(0); // 到手价计算
        
        // 创建测试商品列表
        List<Trolley> orderNow = new ArrayList<>();
        orderNow.add(createTestTrolley("TEST_SKU_001", 2, 0)); // 普通商品
        orderNow.add(createTestTrolley("TEST_SKU_002", 1, 0)); // 普通商品
        placeOrderVO.setOrderNow(orderNow);
        
        return placeOrderVO;
    }

    /**
     * 创建省心送PlaceOrderVO
     */
    public static PlaceOrderVO createTimingPlaceOrderVO() {
        PlaceOrderVO placeOrderVO = createBasicPlaceOrderVO();
        placeOrderVO.setTimingSkuPrice(1); // 省心送
        placeOrderVO.setTimingRuleId(1001); // 省心送规则ID
        return placeOrderVO;
    }

    /**
     * 创建测试购物车项
     */
    public static Trolley createTestTrolley(String sku, Integer quantity, Integer productType) {
        Trolley trolley = new Trolley();
        trolley.setSku(sku);
        trolley.setQuantity(quantity);
        trolley.setProductType(productType);
        trolley.setSuitId(0);
        return trolley;
    }

    /**
     * 创建单店用户上下文
     */
    public static UserContextParam createSingleStoreUserContext() {
        UserContextParam userContext = new UserContextParam();
        userContext.setMId(10001L);
        userContext.setAccountId(20001L);
        userContext.setMname("测试单店用户");
        userContext.setMajorMerchant(false);
        userContext.setDirect(2); // 现结
        userContext.setSkuShow(2); // 全量
        userContext.setServer(1); // 服务区内
        userContext.setBusinessLine(0); // 鲜沐
        userContext.setSize("单店");
        userContext.setHelpOrder(0);
        userContext.setOutTimes(0);
        userContext.setOpenId("test_openid_001");
        
        // 设置区域信息
        Area area = new Area();
        area.setAreaNo(1001);
        area.setAreaName("测试区域");
        userContext.setArea(area);
        
        return userContext;
    }

    /**
     * 创建大客户用户上下文
     */
    public static UserContextParam createMajorMerchantUserContext() {
        UserContextParam userContext = new UserContextParam();
        userContext.setMId(10002L);
        userContext.setAccountId(20002L);
        userContext.setMname("测试大客户");
        userContext.setMajorMerchant(true);
        userContext.setAdminId(1001);
        userContext.setDirect(1); // 账期
        userContext.setSkuShow(1); // 定量
        userContext.setServer(1); // 服务区内
        userContext.setBusinessLine(0); // 鲜沐
        userContext.setSize("大客户");
        userContext.setHelpOrder(0);
        userContext.setOutTimes(0);
        userContext.setOpenId("test_openid_002");
        
        // 设置区域信息
        Area area = new Area();
        area.setAreaNo(1001);
        area.setAreaName("测试区域");
        userContext.setArea(area);
        
        return userContext;
    }

    /**
     * 创建POP商城用户上下文
     */
    public static UserContextParam createPopMerchantUserContext() {
        UserContextParam userContext = new UserContextParam();
        userContext.setMId(10003L);
        userContext.setAccountId(20003L);
        userContext.setMname("测试POP商户");
        userContext.setMajorMerchant(false);
        userContext.setDirect(2); // 现结
        userContext.setSkuShow(2); // 全量
        userContext.setServer(1); // 服务区内
        userContext.setBusinessLine(1); // POP
        userContext.setSize("单店");
        userContext.setHelpOrder(0);
        userContext.setOutTimes(0);
        userContext.setOpenId("test_openid_003");
        
        // 设置POP区域信息
        Area area = new Area();
        area.setAreaNo(44240);
        area.setAreaName("POP测试区域");
        userContext.setArea(area);
        
        return userContext;
    }

    /**
     * 创建代下单用户上下文
     */
    public static UserContextParam createHelpOrderUserContext() {
        UserContextParam userContext = createSingleStoreUserContext();
        userContext.setHelpOrder(1); // 代下单
        userContext.setMId(10004L); // 被代下单的用户ID
        userContext.setAccountId(20004L);
        userContext.setMname("被代下单用户");
        return userContext;
    }

    /**
     * 创建测试联系人信息
     */
    public static Contact createTestContact(Long mId) {
        Contact contact = new Contact();
        contact.setmId(mId);
        contact.setContact("测试联系人");
        contact.setPhone("***********");
        contact.setProvince("浙江省");
        contact.setCity("杭州市");
        contact.setArea("余杭区");
        contact.setAddress("测试地址123号");
        contact.setIsDefault(1);
        return contact;
    }

    /**
     * 创建包含多种商品类型的订单
     */
    public static PlaceOrderVO createMixedProductOrderVO() {
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setTakePriceFlag(true);
        placeOrderVO.setIsTakePrice(0);
        
        List<Trolley> orderNow = new ArrayList<>();
        // 普通商品
        orderNow.add(createTestTrolley("NORMAL_SKU_001", 3, 0));
        // 套装商品
        orderNow.add(createTestTrolley("SUIT_SKU_001", 1, 1));
        // 赠品
        orderNow.add(createTestTrolley("GIFT_SKU_001", 2, 2));
        
        placeOrderVO.setOrderNow(orderNow);
        return placeOrderVO;
    }

    /**
     * 创建大批量商品订单（用于性能测试）
     */
    public static PlaceOrderVO createLargeBatchOrderVO(int skuCount) {
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setTakePriceFlag(true);
        placeOrderVO.setIsTakePrice(0);
        
        List<Trolley> orderNow = new ArrayList<>();
        for (int i = 1; i <= skuCount; i++) {
            orderNow.add(createTestTrolley("BATCH_SKU_" + String.format("%03d", i), 1, 0));
        }
        
        placeOrderVO.setOrderNow(orderNow);
        return placeOrderVO;
    }

    /**
     * 创建特定SKU的订单
     */
    public static PlaceOrderVO createSpecificSkuOrderVO(String sku, Integer quantity) {
        PlaceOrderVO placeOrderVO = new PlaceOrderVO();
        placeOrderVO.setTakePriceFlag(true);
        placeOrderVO.setIsTakePrice(0);
        
        List<Trolley> orderNow = new ArrayList<>();
        orderNow.add(createTestTrolley(sku, quantity, 0));
        placeOrderVO.setOrderNow(orderNow);
        
        return placeOrderVO;
    }
}
