package net.summerfarm.mall.service;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.MallApplication;
import net.summerfarm.mall.mapper.CouponBlackAndWhiteMapper;
import net.summerfarm.mall.model.domain.CouponBlackAndWhite;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MallApplication.class)
@ActiveProfiles("dev")
@Slf4j
public class CouponServiceTest {

    @Resource
    private CouponBlackAndWhiteMapper couponBlackAndWhiteMapper;

    @Resource
    private CouponService couponService;

    @Test
    public void testInitCouponBlackOrWhiteListCache_Success() {
        // Arrange
        List<Integer> couponIds = Arrays.asList(1, 2, 19930);

        List<CouponBlackAndWhite> list = couponBlackAndWhiteMapper.findByCouponIds(couponIds);
        log.info("list:{}", list);

        // Act
        couponService.initCouponBlackOrWhiteListCache(couponIds);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));

        Map<Integer, Set<String>> cacheResult = couponService.getCouponBlackAndWhiteMap(19930);
        log.info("cacheResult:{}", cacheResult);
        Assert.assertTrue(MapUtils.isNotEmpty(cacheResult));
    }
}

