package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.dto.market.malltag.MallTagReqDTO;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.MallTagService;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class MallTagServiceImplTest {

    @Resource
    private MallTagService mallTagService;

    @Test
    public void testGetALlTagForRequest() {
        List<String> skuList = Lists.newArrayList("858224100663", "50410572517", "50362188018", "648146200233", "50267008133", "50503134060", "50451383586", "50470114405",
            "4831436824", "858801367547", "858156816332", "50233218267", "50405516483", "4831436047", "2150765421442", "2150126242811", "2150638233702", "2150127004378",
            "2150676846424", "2150625763034", "858872761703", "858645566376", "50410254264", "50885532658", "858451763470", "50255707802", "2150442466600", "2150442466686",
            "50100848623", "2150442466210", "50241283017", "2150442466613", "1113818817002", "1113432848047", "1113248401110", "1113032673103", "1113745517041", "1113388273838",
            "1113111427716", "1113822872033");
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject("mall__60a106c2-d1dc-465c-a8e5-963d605cc75b", null);
        MallTagReqDTO reqDTO = new MallTagReqDTO();
        reqDTO.setSkus(skuList);
        for (int i = 0; i < 10; i++) {
            long startedAt = System.currentTimeMillis();
            CommonResult<List<SkuMallTagInfoDTO>> result = mallTagService.listAllTags(reqDTO);
            double rt = (System.currentTimeMillis() - startedAt) * 1.00D / 1000;
            log.info("CommonResult<List<SkuMallTagInfoDTO>>, Round:{}, RT:{}s\n{}", i, rt, JSON.toJSONString(result));
        }
    }
}
