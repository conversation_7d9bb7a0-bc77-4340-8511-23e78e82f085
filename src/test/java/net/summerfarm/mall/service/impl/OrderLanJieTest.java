package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.contexts.WholeOrderWorkflow;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


@SpringBootTest
@RunWith(SpringRunner.class)
public class OrderLanJieTest {

    @Resource
    private WholeOrderWorkflow wholeOrderWorkflow;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Test
    public void HandleTest(){
        String s = "{\"afterSaleOrderNo\":\"20601132050011\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"handleNum\":10,\"handleType\":11,\"handler\":\"系统默认\",\"orderNo\":\"01165295147951716\",\"recoveryType\":1,\"totalNumber\":0}";

        /**
         *  整单拦截审核：afterSaleOrderVOAfterSaleOrderVO(pdName=null, picturePath=null, amount=null, weight=null, deliveryStatus=null,
         *  maturity=null, price=null, totalPrice=null, orderTime=null, mname=null, afterSaleTime=0, afterSaleQuantity=0,
         *  categoryId=0, suitOrderItem=null, afterSaleProofList=null, overTime=null, proofPic=null, handleType=11, handleNum=35,
         *  handler=黄云龙, auditer=null, applyRemark=null, updatetime=null, quantity=1, handleRemark=质量, extraRemark=, afterSaleType=null,
         *  refundType=客户原因, saleUnit=null, waitInRedPack=null, accountContact=null, accountPhone=null,
         *  deliveryDate=null, deliveryQuantity=null, address=null, couponId=null, auditeRemark=null, applyer=null, recoveryNum=30,
         *  exchangeGoodList=null)
         */
        AfterSaleOrderVO afterSaleOrderVO = JSON.parseObject(s, AfterSaleOrderVO.class);

        afterSaleOrderService.handle(afterSaleOrderVO);
        System.out.println(afterSaleOrderVO.getHandleType());
    }

    @Test
    public void AuditTest(){
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setAfterSaleOrderNo("**************");
        afterSaleOrderVO.setAuditer("系统默认");
        afterSaleOrderVO.setStatus(1);
        afterSaleOrderService.audit(afterSaleOrderVO);
    }

    @Test
    public void buyCouponInfoTest(){
//        AjaxResult result = advanceSaleService.buyCouponInfo(102547L);

//        String s = JSON.toJSONString(result);
//        System.out.println(s);
    }

    @Test
    public void MQTest(){
        for (int i = 0; i < 30; i++) {
            //发送钉钉消息
            MQData mqData = new MQData();
            mqData.setType(MType.INTERCEPT_ORDER.name());
            mqData.setData("*****************");
            mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
        }

    }
    @Test
    public void afterSaleEntryBillTest(){

        AjaxResult result = afterSaleOrderService.afterSaleEntryBill("************");
        System.out.println(result);
    }


    @Test
    public void saveTest() throws InterruptedException {
        String s = "{\"accountId\":1227,\"addTime\":\"2022-06-20T13:43:31.506\",\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleUnit\":\"1\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":5.00,\"handleRemark\":\"\",\"handleType\":3,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":1417,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"其他\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"totalNumber\":0,\"view\":0}";
        String c = "{\"accountId\":1897,\"addTime\":\"2022-07-20T17:24:11.131\",\"afterSaleQuantity\":0,\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"1\",\"applyer\":\"系统默认\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":172,\"handleType\":2,\"handler\":\"系统默认\",\"isManage\":true,\"mId\":103088,\"orderNo\":\"*****************\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"缺货\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"totalNumber\":0,\"view\":0}";

        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(c, AfterSaleOrderVO.class);

        afterSaleOrderService.save(afterSaleOrderVO1);
    }

}
