# CompleteDeliveryService.queryCompleteDeliveryTime 测试用例文档

## 概述

本文档详细说明了为 `CompleteDeliveryServiceImpl.queryCompleteDeliveryTime` 方法创建的综合测试用例。该方法用于查询配送完成时间，是配送系统的核心功能之一。

## 测试文件结构

### 1. CompleteDeliveryServiceImplTest.java
**主要单元测试文件** - 使用 Mockito 进行纯单元测试

### 2. CompleteDeliveryServiceIntegrationTest.java  
**集成测试文件** - 在 Spring Boot 环境中进行集成测试

## 测试用例分类

### 一、场景测试用例 (Scenario-based Test Cases)

#### 1.1 普通商户场景
- **测试方法**: `testQueryCompleteDeliveryTime_NormalMerchant_Success`
- **场景描述**: 普通商户查询配送完成时间
- **输入参数**: 
  - Contact: mId=12345L, contactId=67890L, city="杭州市", area="余杭区", storeNo=101
  - isMajor=false
- **预期结果**: 返回 LocalTime.of(18, 30)
- **验证点**: 
  - 返回时间正确
  - 外部服务调用参数正确
  - adminId 为 null

#### 1.2 大客户场景 - 有AdminId
- **测试方法**: `testQueryCompleteDeliveryTime_MajorCustomer_WithAdminId`
- **场景描述**: 大客户且有管理员ID的情况
- **输入参数**:
  - Contact: mId=54321L, contactId=98765L, city="上海市", area="浦东新区", storeNo=202
  - isMajor=true
  - RequestHolder.getAdminId()=1507
- **预期结果**: 返回 LocalTime.of(16, 0)
- **验证点**: adminId 正确设置为 "1507"

#### 1.3 大客户场景 - AdminId为空
- **测试方法**: `testQueryCompleteDeliveryTime_MajorCustomer_NullAdminId`
- **场景描述**: 大客户但AdminId为空的情况
- **验证点**: adminId 在请求中为 null

#### 1.4 早配送时间场景
- **测试方法**: `testQueryCompleteDeliveryTime_EarlyDelivery`
- **场景描述**: 上午配送时间场景
- **预期结果**: LocalTime.of(8, 30)
- **验证点**: 时间在12:00之前

#### 1.5 晚配送时间场景
- **测试方法**: `testQueryCompleteDeliveryTime_LateDelivery`
- **场景描述**: 夜间配送时间场景
- **预期结果**: LocalTime.of(22, 0)
- **验证点**: 时间在18:00之后

### 二、边界条件和异常测试用例

#### 2.1 空值处理
- **Contact为null**: 返回 null，不调用外部服务
- **Contact.mId为null**: 返回 null，不调用外部服务
- **外部服务返回null**: 返回 null
- **DeliveryAlertRes.lastDeliveryTime为null**: 返回 null

#### 2.2 极端时间值
- **午夜时间**: LocalTime.of(0, 0)
- **23:59时间**: LocalTime.of(23, 59)

#### 2.3 异常处理
- **外部服务异常**: 抛出 RuntimeException

### 三、API/接口测试用例

#### 3.1 参数验证
- **Contact字段完整性**: 验证所有字段正确传递到外部服务
- **返回值类型验证**: 确保返回 LocalTime 类型
- **方法签名合规性**: 验证方法签名符合接口定义

#### 3.2 数据完整性
- **空字符串字段**: city="" 和 area=""
- **StoreNo为null**: 验证 null 值正确传递

### 四、业务场景组合测试

#### 4.1 特定客户场景
- **喜茶大客户**: adminId=1507 (生产环境)
- **测试环境大客户**: adminId=1026 (测试环境)

#### 4.2 多维度场景
- **多城市配送时间差异**: 北京 vs 上海
- **同商户多地址**: 家庭地址 vs 办公地址

#### 4.3 性能测试
- **多次调用一致性**: 验证相同参数多次调用结果一致

## 测试数据说明

### Contact 测试数据模板
```java
Contact contact = new Contact();
contact.setmId(12345L);           // 商户ID
contact.setContactId(67890L);     // 联系人ID  
contact.setCity("杭州市");         // 城市
contact.setArea("余杭区");         // 区域
contact.setStoreNo(101);          // 仓库编号
contact.setContact("测试联系人");   // 联系人姓名
contact.setPhone("13800138000");  // 电话
contact.setAddress("测试地址123号"); // 地址
```

### 时间测试数据
- **正常配送时间**: 16:00 - 20:00
- **早配送时间**: 8:00 - 12:00  
- **晚配送时间**: 20:00 - 22:00
- **极端时间**: 00:00, 23:59
- **精确时间**: 包含秒和纳秒

## 运行测试

### 单元测试
```bash
# 运行所有单元测试
mvn test -Dtest=CompleteDeliveryServiceImplTest

# 运行特定测试方法
mvn test -Dtest=CompleteDeliveryServiceImplTest#testQueryCompleteDeliveryTime_NormalMerchant_Success
```

### 集成测试
```bash
# 运行集成测试
mvn test -Dtest=CompleteDeliveryServiceIntegrationTest

# 在Spring Boot环境中运行
mvn spring-boot:test
```

## 测试覆盖率

### 代码覆盖率目标
- **行覆盖率**: 100%
- **分支覆盖率**: 100%
- **方法覆盖率**: 100%

### 覆盖的业务逻辑
1. ✅ Contact 空值检查
2. ✅ mId 空值检查  
3. ✅ isMajor 分支逻辑
4. ✅ RequestHolder.getAdminId() 调用
5. ✅ 外部服务调用
6. ✅ 响应空值处理
7. ✅ 时间值返回

## 扩展测试用例建议

### 1. 性能测试
- 并发调用测试
- 大量数据测试
- 响应时间测试

### 2. 安全测试  
- SQL注入测试
- 参数篡改测试
- 权限验证测试

### 3. 兼容性测试
- 不同时区测试
- 不同地区配送规则测试
- 版本兼容性测试

## Mock 策略说明

### 外部依赖 Mock
- **WncDeliveryAlertQueryFacade**: 使用 @Mock 注解
- **RequestHolder**: 使用 MockedStatic 进行静态方法 Mock

### Mock 数据一致性
- 确保 Mock 数据符合真实业务场景
- 使用有意义的测试数据而非随机值
- 保持测试数据的可读性和可维护性

## 最佳实践

### 1. 测试命名规范
- 使用 `@DisplayName` 提供中文描述
- 方法名采用 `testMethodName_Scenario_ExpectedResult` 格式

### 2. 断言策略
- 使用具体的断言而非通用断言
- 提供清晰的断言失败消息
- 验证关键业务逻辑点

### 3. 测试数据管理
- 使用辅助方法创建测试数据
- 保持测试数据的独立性
- 避免测试间的数据依赖

### 4. 异常测试
- 覆盖所有可能的异常场景
- 验证异常类型和消息
- 确保异常不会导致数据不一致

## 维护指南

### 代码变更时的测试更新
1. 新增参数时，更新相关测试用例
2. 修改业务逻辑时，调整对应的验证点
3. 重构代码时，保持测试用例的有效性

### 定期维护任务
1. 检查测试数据的时效性
2. 更新 Mock 数据以反映真实场景
3. 优化测试执行性能
4. 清理过时的测试用例
