package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.service.CompleteDeliveryService;
import net.summerfarm.mall.service.facade.WncDeliveryAlertQueryFacade;
import net.summerfarm.mall.service.facade.dto.DeliveryAlertReq;
import net.summerfarm.mall.service.facade.dto.DeliveryAlertRes;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * CompleteDeliveryService集成测试
 * 
 * 这个测试类演示了如何在Spring Boot环境中测试queryCompleteDeliveryTime方法
 * 包括真实的Spring容器环境和依赖注入
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@SpringBootTest
@SpringJUnitConfig
@DisplayName("CompleteDeliveryService 集成测试")
class CompleteDeliveryServiceIntegrationTest {

    @Resource
    private CompleteDeliveryService completeDeliveryService;

    @MockBean
    private WncDeliveryAlertQueryFacade wncDeliveryAlertQueryFacade;

    @Test
    @DisplayName("集成测试1: 完整业务流程测试")
    void testQueryCompleteDeliveryTime_FullBusinessFlow() {
        // Given: 模拟真实业务场景
        Contact contact = createRealBusinessContact();
        boolean isMajor = false;
        LocalTime expectedDeliveryTime = LocalTime.of(18, 30);

        // Mock外部服务响应
        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedDeliveryTime);
        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When: 调用服务方法
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then: 验证结果
        assertNotNull(result, "集成测试应返回有效结果");
        assertEquals(expectedDeliveryTime, result, "应返回预期的配送时间");

        // 验证外部服务调用
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req -> {
            assertNotNull(req, "请求对象不应为空");
            assertEquals(contact.getContactId(), req.getContactId(), "ContactId应正确传递");
            assertEquals(contact.getCity(), req.getCity(), "City应正确传递");
            assertEquals(contact.getArea(), req.getArea(), "Area应正确传递");
            assertEquals(contact.getStoreNo(), req.getStoreNo(), "StoreNo应正确传递");
            assertEquals(contact.getmId().toString(), req.getMerchantId(), "MerchantId应正确传递");
            assertNull(req.getAdminId(), "普通商户AdminId应为空");
            return true;
        }));
    }

    @Test
    @DisplayName("集成测试2: 大客户业务流程测试")
    void testQueryCompleteDeliveryTime_MajorCustomerFlow() {
        // Given: 大客户业务场景
        Contact contact = createMajorCustomerContact();
        boolean isMajor = true;
        LocalTime expectedDeliveryTime = LocalTime.of(16, 0);

        // Mock RequestHolder返回大客户AdminId
        try (var mockedRequestHolder = mockStatic(RequestHolder.class)) {
            mockedRequestHolder.when(RequestHolder::getAdminId).thenReturn(1507);

            // Mock外部服务响应
            DeliveryAlertRes mockResponse = new DeliveryAlertRes();
            mockResponse.setLastDeliveryTime(expectedDeliveryTime);
            when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                    .thenReturn(mockResponse);

            // When: 调用服务方法
            LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

            // Then: 验证结果
            assertNotNull(result, "大客户集成测试应返回有效结果");
            assertEquals(expectedDeliveryTime, result, "应返回大客户配送时间");

            // 验证大客户请求参数
            verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req -> {
                assertEquals("1507", req.getAdminId(), "大客户AdminId应正确设置");
                return true;
            }));
        }
    }

    @Test
    @DisplayName("集成测试3: 异常场景处理测试")
    void testQueryCompleteDeliveryTime_ExceptionHandling() {
        // Given: 外部服务异常场景
        Contact contact = createRealBusinessContact();
        boolean isMajor = false;

        // Mock外部服务抛出异常
        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenThrow(new RuntimeException("网络连接超时"));

        // When & Then: 验证异常处理
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);
        }, "应抛出运行时异常");

        assertEquals("网络连接超时", exception.getMessage(), "异常消息应正确传递");
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(any(DeliveryAlertReq.class));
    }

    @Test
    @DisplayName("集成测试4: 空值处理测试")
    void testQueryCompleteDeliveryTime_NullHandling() {
        // Given: 各种空值场景
        
        // 测试Contact为null
        LocalTime result1 = completeDeliveryService.queryCompleteDeliveryTime(null, false);
        assertNull(result1, "Contact为null时应返回null");

        // 测试Contact.mId为null
        Contact contactWithNullMId = new Contact();
        contactWithNullMId.setContactId(12345L);
        contactWithNullMId.setmId(null);
        
        LocalTime result2 = completeDeliveryService.queryCompleteDeliveryTime(contactWithNullMId, false);
        assertNull(result2, "mId为null时应返回null");

        // 测试外部服务返回null
        Contact validContact = createRealBusinessContact();
        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(null);
        
        LocalTime result3 = completeDeliveryService.queryCompleteDeliveryTime(validContact, false);
        assertNull(result3, "外部服务返回null时应返回null");

        // 验证只有最后一个场景调用了外部服务
        verify(wncDeliveryAlertQueryFacade, times(1)).queryDeliveryAlert(any(DeliveryAlertReq.class));
    }

    @Test
    @DisplayName("集成测试5: 数据类型和精度测试")
    void testQueryCompleteDeliveryTime_DataTypeAndPrecision() {
        // Given: 测试时间精度和数据类型
        Contact contact = createRealBusinessContact();
        boolean isMajor = false;
        
        // 测试包含秒和纳秒的精确时间
        LocalTime preciseTime = LocalTime.of(14, 30, 45, 123456789);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(preciseTime);
        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then: 验证精确时间处理
        assertNotNull(result, "精确时间结果不应为空");
        assertEquals(preciseTime, result, "应保持时间精度");
        assertEquals(14, result.getHour(), "小时应正确");
        assertEquals(30, result.getMinute(), "分钟应正确");
        assertEquals(45, result.getSecond(), "秒数应正确");
        assertEquals(123456789, result.getNano(), "纳秒应正确");
    }

    // ========== 辅助方法 ==========

    /**
     * 创建真实业务场景的Contact对象
     */
    private Contact createRealBusinessContact() {
        Contact contact = new Contact();
        contact.setmId(349548L); // 真实商户ID
        contact.setContactId(123456L);
        contact.setContact("张三");
        contact.setPhone("***********");
        contact.setProvince("浙江省");
        contact.setCity("杭州市");
        contact.setArea("余杭区");
        contact.setAddress("文一西路969号");
        contact.setStoreNo(1001);
        contact.setIsDefault(1);
        return contact;
    }

    /**
     * 创建大客户场景的Contact对象
     */
    private Contact createMajorCustomerContact() {
        Contact contact = new Contact();
        contact.setmId(888888L); // 大客户商户ID
        contact.setContactId(999999L);
        contact.setContact("李四");
        contact.setPhone("***********");
        contact.setProvince("广东省");
        contact.setCity("深圳市");
        contact.setArea("福田区");
        contact.setAddress("深南大道1000号");
        contact.setStoreNo(2001);
        contact.setIsDefault(1);
        return contact;
    }
}
