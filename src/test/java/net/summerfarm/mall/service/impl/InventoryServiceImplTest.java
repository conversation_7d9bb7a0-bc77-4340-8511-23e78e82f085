package net.summerfarm.mall.service.impl;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.input.product.ProductSearchInput;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.InventoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev2")
@TestPropertySource(properties = {"xm.mq.listen=false", "dubbo.protocol.port=20881", // Automatically assign an available port
        "dubbo.protocol.name=dubbo"})
class InventoryServiceImplTest {

    private MockedStatic<RequestHolder> mockedRequestHolder;

    @BeforeEach
    void setUp() {
        MerchantSubject merchantSubject = new MerchantSubject();
        merchantSubject.setSize("单店");
        merchantSubject.setArea(new Area(1001));
        merchantSubject.setMerchantId(349548L);
        mockedRequestHolder = Mockito.mockStatic(RequestHolder.class);
        mockedRequestHolder.when(RequestHolder::getMerchantSubject).thenReturn(merchantSubject);
        mockedRequestHolder.when(RequestHolder::getMerchantAreaNo).thenReturn(1001);
        mockedRequestHolder.when(RequestHolder::getMerchantArea).thenReturn(new Area(1001));
        mockedRequestHolder.when(RequestHolder::getMerchantId).thenReturn(349548L);
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(349548L);
    }

    @AfterEach
    void tearDown() {
        if (mockedRequestHolder != null) {
            mockedRequestHolder.close();
        }
    }


    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private InventoryService inventoryService;

    @Test
    void testGetInventoryBySku() {
        String sku = "test-sku";
        Inventory inventory1 = new Inventory();
        inventory1.setSku(sku);
        inventory1.setQuantity(100);

        Inventory inventory2 = new Inventory();
        inventory2.setSku(sku);
        inventory2.setQuantity(50);

        Inventory inventory = inventoryService.selectInventoryWithCache("11223344");

        assertEquals(inventory, null);

        AjaxResult<PageInfo<ProductInfoVO>> pageResult = inventoryService.selectSkuList(1, 20, "fake-table-name");
        log.info("pageResult: {}", pageResult);
        assertTrue(pageResult != null && pageResult.getData() == null);

        ProductSearchInput searchInput = new ProductSearchInput();
        searchInput.setPdName("安佳");
        PageInfo<ProductInfoVO> productInfoVOPageInfo = inventoryService.selectHomeProductVoV2(1, 10, searchInput);
        log.info("{}, 搜索结果:{}", searchInput, productInfoVOPageInfo);
        assertTrue(productInfoVOPageInfo != null && CollectionUtils.isNotEmpty(productInfoVOPageInfo.getList()));
        long pdIdToTest = productInfoVOPageInfo.getList().get(0).getPdId();
        log.info("pdIdToTest: {}", pdIdToTest);
        AjaxResult<List<ProductInfoVO>> productInfoOfPdId = inventoryService.selectProductInfo(pdIdToTest);
        log.info("productInfoOfPdId: {}", productInfoOfPdId);
        assertTrue(productInfoOfPdId != null && CollectionUtils.isNotEmpty(productInfoOfPdId.getData()));
        assertTrue(productInfoOfPdId.getData().get(0).getPdId() == pdIdToTest);
        assertTrue(productInfoOfPdId.getData().get(0).getPdName().contains("安佳"));

        Set<String> skuSet = productInfoVOPageInfo.getList().stream().map(ProductInfoVO::getSku).collect(Collectors.toSet());
        Map<String, Inventory> cachedInventories = inventoryService.selectInventoryWithCacheBatch(skuSet);
        log.info("cachedInventories: {}", cachedInventories);
        assertTrue(cachedInventories != null && !cachedInventories.isEmpty());
        assertTrue(cachedInventories.keySet().containsAll(skuSet));

        Inventory inventoryFromCache = inventoryService.selectInventoryWithCache(productInfoVOPageInfo.getList().get(0).getSku());
        log.info("inventoryFromCache:{}", inventoryFromCache);
        assertTrue(null != inventoryFromCache && skuSet.contains(inventoryFromCache.getSku()));
    }
}
