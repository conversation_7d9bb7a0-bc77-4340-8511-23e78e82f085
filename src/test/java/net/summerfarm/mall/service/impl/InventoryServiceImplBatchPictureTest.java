package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.ProductsMapper;
import net.summerfarm.mall.model.domain.Inventory;
import net.summerfarm.mall.model.domain.Products;
import net.summerfarm.mall.model.vo.TimingProductVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.Mockito.when;

/**
 * 测试批量图片加载优化
 */
@ExtendWith(MockitoExtension.class)
public class InventoryServiceImplBatchPictureTest {

    @Mock
    private InventoryMapper inventoryMapper;

    @Mock
    private ProductsMapper productsMapper;

    @InjectMocks
    private InventoryServiceImpl inventoryService;

    @BeforeEach
    void setUp() {
        // 注入mock对象到私有字段
        ReflectionTestUtils.setField(inventoryService, "inventoryMapper", inventoryMapper);
        ReflectionTestUtils.setField(inventoryService, "productsMapper", productsMapper);
    }

    @Test
    void testBatchReplenishTimingProductPic_WithInventoryPictures() throws Exception {
        // 准备测试数据
        TimingProductVO product1 = new TimingProductVO();
        product1.setSku("SKU001");
        product1.setPdId(1);

        TimingProductVO product2 = new TimingProductVO();
        product2.setSku("SKU002");
        product2.setPdId(2);

        List<TimingProductVO> productList = Arrays.asList(product1, product2);

        // Mock库存数据
        Inventory inventory1 = new Inventory();
        inventory1.setSku("SKU001");
        inventory1.setSkuPic("inventory_pic_1.jpg");

        Inventory inventory2 = new Inventory();
        inventory2.setSku("SKU002");
        inventory2.setSkuPic("inventory_pic_2.jpg");

        when(inventoryMapper.listBySkus(anyCollection()))
                .thenReturn(Arrays.asList(inventory1, inventory2));

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(inventoryService, "batchReplenishTimingProductPic", productList);

        // 验证结果
        assertEquals("inventory_pic_1.jpg", product1.getDetailPicture());
        assertEquals("inventory_pic_2.jpg", product2.getDetailPicture());
    }

    @Test
    void testBatchReplenishTimingProductPic_WithProductPictures() throws Exception {
        // 准备测试数据
        TimingProductVO product1 = new TimingProductVO();
        product1.setSku("SKU001");
        product1.setPdId(1);

        List<TimingProductVO> productList = Collections.singletonList(product1);

        // Mock库存数据（没有图片）
        Inventory inventory1 = new Inventory();
        inventory1.setSku("SKU001");
        inventory1.setSkuPic(null);

        when(inventoryMapper.listBySkus(anyCollection()))
                .thenReturn(Collections.singletonList(inventory1));

        // Mock产品数据
        Products products1 = new Products();
        products1.setPdId(1L);
        products1.setPicturePath("product_pic_1.jpg");

        when(productsMapper.selectByPdIds(anyCollection()))
                .thenReturn(Collections.singletonList(products1));

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(inventoryService, "batchReplenishTimingProductPic", productList);

        // 验证结果
        assertEquals("product_pic_1.jpg", product1.getDetailPicture());
    }

    @Test
    void testBatchReplenishTimingProductPic_EmptyList() throws Exception {
        // 测试空列表
        List<TimingProductVO> emptyList = Collections.emptyList();

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(inventoryService, "batchReplenishTimingProductPic", emptyList);

        // 验证没有异常抛出，方法正常返回
    }

    @Test
    void testBatchReplenishTimingProductPic_NoPicturesFound() throws Exception {
        // 准备测试数据
        TimingProductVO product1 = new TimingProductVO();
        product1.setSku("SKU001");
        product1.setPdId(1);

        List<TimingProductVO> productList = Collections.singletonList(product1);

        // Mock库存数据（没有图片）
        Inventory inventory1 = new Inventory();
        inventory1.setSku("SKU001");
        inventory1.setSkuPic(null);

        when(inventoryMapper.listBySkus(anyCollection()))
                .thenReturn(Collections.singletonList(inventory1));

        // Mock产品数据（没有图片）
        Products products1 = new Products();
        products1.setPdId(1L);
        products1.setPicturePath(null);

        when(productsMapper.selectByPdIds(anyCollection()))
                .thenReturn(Collections.singletonList(products1));

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(inventoryService, "batchReplenishTimingProductPic", productList);

        // 验证结果 - 图片应该保持为null
        assertNull(product1.getDetailPicture());
    }
}
