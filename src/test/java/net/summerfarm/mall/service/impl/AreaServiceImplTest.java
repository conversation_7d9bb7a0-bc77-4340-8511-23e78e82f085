package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.service.AreaService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@SpringBootTest
@RunWith(SpringRunner.class)
public class AreaServiceImplTest {

    @Resource
    private AreaService areaService;

    @Resource
    private AreaMapper areaMapper;

    @Test
    public void updateAreaCache() {
        log.info("updated cache:", areaService.updateAreaCache(1001));
        Area area1001 = areaMapper.selectByAreaNo(1001);
        Area area1001FromCache = areaService.selectAreaWithCache(1001);
        log.info("get from cache:{}, from database:{}", area1001FromCache, area1001);
        Assert.assertTrue(JSON.toJSONString(area1001FromCache).equalsIgnoreCase(JSON.toJSONString(area1001)));
    }
}
