package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.mall.after.template.AfterSaleOrderCreatHandler;
import net.summerfarm.mall.mapper.AfterSaleOrderMapper;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.helper.AfterHandleTypeExecuteHelper;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class AfterSaleOrderTest {

    @Resource
    private OrderService orderService;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterSaleOrderCreatHandler afterSaleOrderCreatHandler;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterHandleTypeExecuteHelper afterHandleTypeExecuteHelper;
    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;
    /**
     * 创建售后单号重复校验
     */
    @Test
    public void createAfterSaleNoTest(){
        CountDownLatch countDownLatch = new CountDownLatch(10);

        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(5, 10, 1L,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), new ThreadPoolExecutor.CallerRunsPolicy());

        for (int i = 0; i < 10; i++) {
            poolExecutor.execute(()->{
                HashSet<String> ids = new HashSet<>();
                for (int j = 0; j < 20000; j++) {
                    long l = SnowflakeUtil.nextId();
                    if (ids.contains(l)){
                        throw new RuntimeException("ID生成重复");
                    }
                    ids.add(l+"");
                }
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.info("异常",e);
        }
    }

    @Test
    public void calcRefund(){
        String orderNo = "*****************";
        String sku = "************";
        Integer quantity =10 ;
        Integer suitId = 329;
        Integer deliveryed = 1;
        Integer type = 0;
        Integer deliveryId = null;
        Integer handleType = 2;
        afterSaleOrderService.calcAfterSaleCoupon(orderNo, sku,quantity,suitId, deliveryed, type,deliveryId,handleType,null);
    }

    @Test
    public void saveTest() throws InterruptedException {
        String z = "{\"accountId\":1953,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":54.67,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":256722,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String q = "{\"accountId\":1503,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"2\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":26.28,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":1321,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String c = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.7,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String n = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.7,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String m = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.7,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String tmd = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.7,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String qnmd = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.7,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String ca = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.7,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String nm = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"1\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":80,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String sw = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"1\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":100,\"handleRemark\":\"\",\"handleType\":0,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String ws = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"exchangeGoodList\":[{\"pdName\":\"春天的桃子\",\"quantity\":2,\"sku\":\"************\",\"weight\":\"4个*1斤/一级\"}],\"handleNum\":0,\"handleRemark\":\"\",\"handleType\":6,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":10,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":3,\"view\":0}";
        String wq = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"a\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":0,\"handleRemark\":\"\",\"handleType\":7,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":3,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":5,\"view\":0}";
        String wqs= "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":11.41,\"handleRemark\":\"\",\"handleType\":9,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":30,\"recoveryType\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":9,\"view\":0}";
        String wqq="{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":5.7,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":11,\"view\":0}";
        String wzx = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":2.9,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String cx = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":10.9,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String wzxc = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":21.81,\"handleRemark\":\"\",\"handleType\":4,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":10,\"recoveryType\":1,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String wqzcv = "{\"accountId\":1954,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleUnit\":\"1\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":90,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":256723,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"缺货\",\"sku\":\"************\",\"status\":0,\"suitId\":305,\"times\":1,\"view\":0}";
        String sx = "{\"accountId\":1539,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":0,\"handleRemark\":\"\",\"handleType\":7,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":102730,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String op="{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":false,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"suitId\":303,\"type\":0}";
        String cv = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"个\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":23.41,\"handleType\":2,\"isManage\":false,\"orderNo\":\"01166080341057137\",\"proofPic\":\"\",\"quantity\":1,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"suitId\":0,\"type\":0}";
        String ma = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":107,\"handleType\":2,\"isManage\":false,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"suitId\":303,\"type\":0}";
        String we = "{\"accountId\":1539,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"商品数量不符\",\"afterSaleUnit\":\"个\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":200,\"handleRemark\":\"\",\"handleType\":3,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":102730,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":8,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":6,\"view\":0}";
        String cow = "{\"accountId\":1529,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"33\",\"afterSaleRemarkType\":1,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyer\":\"测试哈哈\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":0.3,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"测试哈哈\",\"isManage\":true,\"mId\":102720,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":10,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"其他\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String nmc = "{\"accountId\":1954,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"333\",\"afterSaleRemarkType\":2,\"afterSaleTime\":0,\"afterSaleType\":\"商品品质问题\",\"afterSaleUnit\":\"件\",\"applyer\":\"测试哈哈\",\"categoryId\":0,\"deliveryed\":1,\"exchangeGoodList\":[{\"pdName\":\"朱永林秒杀商品\",\"quantity\":2,\"sku\":\"************\",\"weight\":\"10G*10L/12/11(23)\"}],\"handleNum\":0,\"handleRemark\":\"\",\"handleType\":6,\"handler\":\"测试哈哈\",\"isManage\":true,\"mId\":256723,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":2,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":4,\"view\":0}";
        String xma = "{\"accountId\":1954,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"3123\",\"afterSaleRemarkType\":1,\"afterSaleTime\":0,\"afterSaleType\":\"商品数量不符\",\"afterSaleUnit\":\"件\",\"applyer\":\"测试哈哈\",\"categoryId\":0,\"deliveryed\":1,\"exchangeGoodList\":[{\"pdName\":\"朱永林秒杀商品\",\"quantity\":1,\"sku\":\"************\",\"weight\":\"10G*10L/12/11(23)\"}],\"handleNum\":0,\"handleRemark\":\"\",\"handleType\":6,\"handler\":\"测试哈哈\",\"isManage\":true,\"mId\":256723,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String tmdb = "{\"accountId\":1954,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":2,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":280,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":256723,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":4,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"其他\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":2,\"view\":0}";
        String kl = "{\"accountId\":1876,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleUnit\":\"3\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":23.41,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":103068,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"缺货\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String nmw = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":272.3,\"handleType\":2,\"isManage\":false,\"mId\":289496,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":5,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"suitId\":0,\"type\":0}";
        String nmaq = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"1\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":15.88,\"handleType\":2,\"isManage\":false,\"mId\":289497,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":6,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"**********\",\"suitId\":0,\"type\":0}";
        String ipw = "{\"accountId\":1963,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":158.71,\"handleRemark\":\"\",\"handleType\":2,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":289497,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":3,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String ps = "{\"accountId\":1259,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"222\",\"afterSaleRemarkType\":8,\"afterSaleTime\":0,\"applyer\":\"昌泰平\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":4,\"handleRemark\":\"111\",\"handleType\":13,\"handler\":\"昌泰平\",\"isManage\":true,\"mId\":1449,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"status\":0,\"times\":1,\"view\":0}";
        String nmd2 = "{\"accountId\":1259,\"addTime\":\"2022-08-30T14:44:32.051\",\"afterSaleQuantity\":0,\"afterSaleRemark\":\"222\",\"afterSaleRemarkType\":8,\"afterSaleTime\":0,\"applyer\":\"昌泰平\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":4,\"handleRemark\":\"111\",\"handleType\":13,\"handler\":\"昌泰平\",\"isManage\":true,\"mId\":1449,\"orderNo\":\"*****************\",\"proofPic\":\"test/i3qkap76k4bftqmz7.png\",\"status\":0,\"times\":2,\"totalNumber\":0,\"view\":0}";
        String msk = "{\"accountId\":1259,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"2\",\"afterSaleRemarkType\":8,\"afterSaleTime\":0,\"applyer\":\"昌泰平\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":4,\"handleRemark\":\"1\",\"handleType\":13,\"handler\":\"昌泰平\",\"isManage\":true,\"mId\":1449,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"status\":0,\"times\":4,\"view\":0}";
        String tmdw = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"1\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":136,\"handleType\":3,\"isManage\":false,\"mId\":289497,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":4,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"***********\",\"suitId\":0,\"type\":0}";
        String owp = "{\"accountId\":1963,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":4,\"handleRemark\":\"\",\"handleType\":3,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":289497,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"缺货\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":2,\"view\":0}";
        String xaw = "{\"accountId\":1963,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":4,\"handleRemark\":\"\",\"handleType\":3,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":289497,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"refundType\":\"缺货\",\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String xm = "{\"accountId\":1333,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":10,\"handleRemark\":\"\",\"handleType\":13,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":2519,\"orderNo\":\"*****************\",\"proofPic\":\"\",\"status\":0,\"times\":1,\"view\":0}";
        String wp1 = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":48,\"handleType\":2,\"isManage\":false,\"mId\":289497,\"orderNo\":\"*****************\",\"productType\":0,\"proofPic\":\"\",\"quantity\":1,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"suitId\":0,\"type\":0}";
        String wpoa = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"g\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":58,\"handleType\":2,\"isManage\":false,\"mId\":289497,\"orderNo\":\"*****************\",\"productType\":0,\"proofPic\":\"\",\"quantity\":1,\"refundType\":\"拍多/拍错/不想要\",\"sku\":\"************\",\"suitId\":0,\"type\":0}";
        String wocaq1 = "{\"accountId\":1924,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"\",\"afterSaleRemarkType\":9,\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applyer\":\"黄云龙\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":100,\"handleRemark\":\"\",\"handleType\":4,\"handler\":\"黄云龙\",\"isManage\":true,\"mId\":256692,\"orderNo\":\"*****************\",\"productType\":0,\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        String wp12 = "{\"accountId\":1924,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"111\",\"afterSaleTime\":0,\"afterSaleType\":\"其他\",\"afterSaleUnit\":\"件\",\"applySecondaryRemark\":\"质量,错货\",\"applyer\":\"昌泰平\",\"categoryId\":0,\"deliveryed\":1,\"handleNum\":28,\"handleRemark\":\"\",\"handleType\":4,\"handler\":\"昌泰平\",\"isManage\":true,\"mId\":256692,\"orderNo\":\"*****************\",\"productType\":0,\"proofPic\":\"\",\"quantity\":1,\"recoveryNum\":0,\"recoveryType\":0,\"sku\":\"************\",\"status\":0,\"suitId\":0,\"times\":1,\"view\":0}";
        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(wp12, AfterSaleOrderVO.class);
//        afterSaleOrderService.save(afterSaleOrderVO1);
        afterSaleOrderService.newSave(afterSaleOrderVO1);
    }

    @Test
    public void handleTest(){
        String w ="{\"afterSaleOrderNo\":\"1557402750202277888\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":5.70,\"handleRemark\":\"质量\",\"handleType\":2,\"handler\":\"黄云龙\",\"orderNo\":\"*****************\",\"quantity\":8,\"sku\":\"************\"}";
        String s = "{\"afterSaleOrderNo\":\"1557416715990011904\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":21.81,\"handleRemark\":\"质量\",\"handleType\":4,\"handler\":\"黄云龙\",\"orderNo\":\"*****************\",\"quantity\":1,\"sku\":\"************\"}";
        String c = "{\"afterSaleOrderNo\":\"8250241698836447232\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":70,\"handleRemark\":\"质量\",\"handleType\":5,\"handler\":\"黄云龙\",\"orderNo\":\"04166122103395672\",\"quantity\":2,\"sku\":\"19422364872\"}";
        String x = "{\"afterSaleOrderNo\":\"8249969752622170112\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":15,\"handleRemark\":\"质量\",\"handleType\":0,\"handler\":\"黄云龙\",\"orderNo\":\"01166115619447732\",\"quantity\":2,\"sku\":\"************\"}";
        String eq = "{\"afterSaleOrderNo\":\"8250338076103213056\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"handleNum\":1.43,\"handleRemark\":\"4123\",\"handleType\":-1,\"handler\":\"测试哈哈\",\"orderNo\":\"01166122610469506\",\"quantity\":32,\"sku\":\"5441008312\"}";
        String iu= "{\"afterSaleOrderNo\":\"8251049307084619776\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"handleNum\":420,\"handleRemark\":\"凭证不符合要求,请重新上传照片\",\"handleType\":-1,\"handler\":\"测试哈哈\",\"orderNo\":\"01166141354849361\",\"quantity\":600,\"sku\":\"************\"}";
        String dhf= "{\"afterSaleOrderNo\":\"8251059692894355456\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":400,\"handleRemark\":\"质量\",\"handleType\":2,\"handler\":\"黄云龙\",\"orderNo\":\"01166141568743754\",\"quantity\":3,\"sku\":\"5466723784\"}";
        String nad = "{\"afterSaleOrderNo\":\"8251444963523624960\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":60,\"handleRemark\":\"质量\",\"handleType\":2,\"handler\":\"黄云龙\",\"orderNo\":\"02166150791481006\",\"quantity\":100,\"sku\":\"************\"}";
        String msl = "{\"afterSaleOrderNo\":\"8253206909600071680\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":95,\"handleRemark\":\"质量\",\"handleType\":11,\"handler\":\"黄云龙\",\"orderNo\":\"01166192803492356\",\"quantity\":2,\"recoveryNum\":30,\"recoveryType\":1,\"refundType\":\"其他\"}";
        String wqp = "{\"afterSaleOrderNo\":\"8268098362235092992\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"handleNum\":48.20,\"handleRemark\":\"啊\",\"handleType\":-1,\"handler\":\"黄云龙\",\"orderNo\":\"01166547710882914\",\"quantity\":1,\"sku\":\"296530606256\"}";
        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(wqp, AfterSaleOrderVO.class);
        afterSaleOrderService.newHandle(afterSaleOrderVO1);
    }

    @Test
    public void auditTest(){
        String c = "{\"afterSaleOrderNo\":\"1557359917480091648\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":0}";
        String a = "{\"afterSaleOrderNo\":\"1557370450023358464\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":1}";
        String w ="{\"afterSaleOrderNo\":\"1557402750202277888\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"extraRemark\":\"\",\"handleNum\":5.70,\"handleRemark\":\"质量\",\"handleType\":2,\"handler\":\"黄云龙\",\"orderNo\":\"*****************\",\"quantity\":8,\"sku\":\"************\"}";
        String x = "{\"afterSaleOrderNo\":\"1558855628277862400\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditeRemark\":\"a\",\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":1}";
        String wq = "{\"afterSaleOrderNo\":\"8250241698836447232\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":1}";
        String tmd = "{\"afterSaleOrderNo\":\"8263312131886350336\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":1}";
        String wpq = "{\"afterSaleOrderNo\":\"8263648614262439936\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"张丽平\",\"categoryId\":0,\"status\":1}";
        String wpq2 = "{\"afterSaleOrderNo\":\"8263744294142279680\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":1}";
        String tmdb = "{\"afterSaleOrderNo\":\"8266325102145241088\",\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"auditer\":\"黄云龙\",\"categoryId\":0,\"status\":1}";
        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(tmdb, AfterSaleOrderVO.class);
        afterSaleOrderService.newAudit(afterSaleOrderVO1);
    }

    @Test
    public void getMaxQuantity(){
        String c = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":6,\"isManage\":true,\"orderNo\":\"*****************\",\"sku\":\"************\",\"suitId\":0}";
        String w2 = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"02166010056674305\",\"sku\":\"167748033712\",\"suitId\":0}";
        String iu = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"deliveryId\":5011950,\"handleType\":0,\"isManage\":true,\"orderNo\":\"02166012641775472\",\"sku\":\"883663885558\",\"suitId\":0}";
        String qw = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryId\":5012058,\"handleType\":0,\"isManage\":true,\"orderNo\":\"02166030013528015\",\"sku\":\"18650638304\",\"suitId\":0}";
        String wq = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166055093653592\",\"sku\":\"968106134366\",\"suitId\":305}";
        String xc = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":0,\"isManage\":true,\"orderNo\":\"01166055093653592\",\"sku\":\"************\",\"suitId\":305}";
        String op = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166056823790485\",\"sku\":\"968106134366\",\"suitId\":305}";
        String lm = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":0,\"isManage\":true,\"orderNo\":\"01166056823790485\",\"sku\":\"968106134366\",\"suitId\":305}";
        String mx = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166057580973216\",\"sku\":\"************\",\"suitId\":305}";
        String mzssw="{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryId\":5590032,\"deliveryed\":1,\"handleType\":0,\"isManage\":true,\"orderNo\":\"02166140654403433\",\"sku\":\"830164715826\",\"suitId\":0}";
        String woqp = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"orderNo\":\"01166375316644582\",\"productType\":2,\"sku\":\"593305756144\",\"suitId\":0}";
        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(woqp, AfterSaleOrderVO.class);
        AjaxResult afterSaleMoney =  afterSaleOrderService.getMaxQuantity(afterSaleOrderVO1);
        System.out.println(afterSaleMoney);
    }

    @Test
    public void delivery(){
        afterSaleOrderService.deliveryStatus("*****************",true,null,null);
    }

    @Test
    public void getMaxMoney(){
        String c = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":4,\"isManage\":true,\"orderNo\":\"*****************\",\"quantity\":5,\"sku\":\"************\",\"suitId\":0}";
        String w = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":0,\"isManage\":true,\"orderNo\":\"*****************\",\"quantity\":10,\"sku\":\"************\",\"suitId\":0}";
        String zx = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":2,\"isManage\":true,\"orderNo\":\"*****************\",\"quantity\":1,\"sku\":\"************\",\"suitId\":0}";
        String op = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"02166010056674305\",\"quantity\":5,\"sku\":\"167748033712\",\"suitId\":0}";
        String x = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryId\":5012058,\"handleType\":0,\"isManage\":true,\"orderNo\":\"02166030013528015\",\"sku\":\"18650638304\",\"suitId\":0}";
        String woq="{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166055093653592\",\"quantity\":1,\"sku\":\"968106134366\",\"suitId\":305}";
        String qs = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166056823790485\",\"quantity\":1,\"sku\":\"************\",\"suitId\":305}";
        String iu = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":0,\"isManage\":true,\"orderNo\":\"01166056823790485\",\"quantity\":1,\"sku\":\"************\",\"suitId\":305}";
        String xz = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"*****************\",\"quantity\":1,\"sku\":\"************\",\"suitId\":303}";
        String xc = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166081520163217\",\"quantity\":1,\"sku\":\"************\",\"suitId\":303}";
        String ll = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"handleType\":3,\"isManage\":true,\"orderNo\":\"*****************\",\"quantity\":1,\"sku\":\"************\",\"suitId\":0}";
        String aw = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"01166149740525190\",\"quantity\":1,\"sku\":\"************\",\"suitId\":0}";
        String wo = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"isManage\":true,\"orderNo\":\"04166305984979227\",\"quantity\":4,\"sku\":\"************\",\"suitId\":0}";
        String wp="{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleType\":2,\"orderNo\":\"01166375316644582\",\"productType\":2,\"quantity\":1,\"sku\":\"593305756144\",\"suitId\":0}";
        String wpq = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":1,\"handleType\":0,\"isManage\":true,\"orderNo\":\"01166424974783988\",\"productType\":0,\"quantity\":1,\"sku\":\"296785215648\",\"suitId\":21}";
        String nMb = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"个\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":0,\"handleType\":2,\"isManage\":false,\"mId\":256722,\"orderNo\":\"01166539054239291\",\"productType\":0,\"proofPic\":\"\",\"sku\":\"************\",\"suitId\":0,\"type\":0}";
        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(wpq, AfterSaleOrderVO.class);
        AjaxResult afterSaleMoney = afterSaleOrderService.getAfterSaleMoney(afterSaleOrderVO1);
        System.out.println(afterSaleMoney);

    }

    @Test
    public void getHandleType(){
        String orderNo = "*****************";
        String sku = "************";
        Integer deliveryed = 1;
        boolean isManage = true;
        afterSaleOrderService.getHandleType(orderNo,sku,deliveryed,isManage);
    }

    @Test
    public void batchSave(){
        String c = "[{\"afterSaleOrderStatus\":1,\"mId\":256722,\"isManage\":true,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"asd\",\"afterSaleTime\":0,\"categoryId\":0,\"deliveryId\":5012194,\"deliveryed\":0,\"handleNum\":0,\"handleType\":11,\"orderNo\":\"02166071787982158\",\"quantity\":0,\"refundType\":\"停配/超区\",\"sku\":\"22805878104\",\"suitId\":0,\"type\":0}]";
        String x = "[{\"afterSaleOrderStatus\":1,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"阿斯顿\",\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleNum\":67.50,\"handleType\":11,\"isManage\":true,\"mId\":256692,\"orderNo\":\"01166113989683396\",\"quantity\":1,\"refundType\":\"停配/超区\",\"suitId\":0,\"type\":0}]";
        String cx = "[{\"afterSaleOrderStatus\":1,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"3123\",\"afterSaleTime\":0,\"categoryId\":0,\"deliveryed\":0,\"handleNum\":183.00,\"handleType\":11,\"isManage\":true,\"mId\":103088,\"orderNo\":\"01166131167369516\",\"quantity\":4,\"refundType\":\"客户原因\",\"suitId\":0,\"type\":0}]";
        List<AfterSaleOrderVO> afterSaleOrderVO1 = JSON.parseArray(cx, AfterSaleOrderVO.class);
        AjaxResult result = afterSaleOrderService.batchSave(afterSaleOrderVO1);
        System.out.println(afterSaleOrderVO1);
        System.out.println(result);
    }

    @Test
    public void coupon(){
        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo("8250663165793337344");
        afterHandleTypeExecuteHelper.coupon(afterSaleOrderVO);
    }

    @Test
    public void dasabi(){
        String nMb = "{\"afterSaleQuantity\":0,\"afterSaleTime\":0,\"afterSaleUnit\":\"个\",\"applyRemark\":\"\",\"categoryId\":0,\"deliveryed\":0,\"handleNum\":0,\"handleType\":2,\"isManage\":false,\"mId\":256722,\"orderNo\":\"01166539054239291\",\"productType\":0,\"proofPic\":\"\",\"sku\":\"************\",\"suitId\":0,\"type\":0}";
        AfterSaleOrderVO afterSaleOrderVO1 = JSON.parseObject(nMb, AfterSaleOrderVO.class);
        BigDecimal bigDecimal = afterSaleOrderHelper.fareMoney(afterSaleOrderVO1);
        System.out.println(bigDecimal);
    }
    @Test
    public void findPage(){
        afterSaleOrderService.findPage(1,20);
    }

    @Test
    public void Json(){
        AjaxResult result = orderService.orderDetails("01166392218194842");
        System.out.println(JSON.toJSONString(result));

        /*ExecutableAfterSale executableAfterSale = new ExecutableAfterSale();
        executableAfterSale.setDelivery(AfterSaleDeliveryedEnum.NOT_NEED.getType());
        AjaxResult ok = AjaxResult.getOK(executableAfterSale);
        System.out.println(JSON.toJSONString(ok));*/
        /*AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setOrderNo("1230");
        afterSaleOrderVO.setSuitId(12);
        afterSaleOrderVO.setSku("123");
        afterSaleOrderVO.setDeliveryed(1);
        afterSaleOrderVO.setDeliveryId(123);
        afterSaleOrderVO.setQuantity(12);
        afterSaleOrderVO.setHandleType(1);
        String s = JSON.toJSONString(afterSaleOrderVO);
        System.out.println(s);*/
        /*Map<Integer, Set<Integer>> orderToHandleType = AfterSaleOrderHelper.orderToHandleType;
        Set<Integer> integers = orderToHandleType.get(1);
        AjaxResult ok = AjaxResult.getOK(integers);
        System.out.println(JSON.toJSONString(ok));*/
        /*afterSaleOrderCreatTemplate.basicCheck(null,null,null);
        double a = 10000;
        double b = 33;
        double c = a/b;
        double ceil = Math.ceil(c);
        System.out.println(ceil);*/

        /*List<AfterSaleOrderVO> afterSaleOrderVOS = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
            afterSaleOrderVO.setOrderNo("21"+i);
            afterSaleOrderVOS.add(afterSaleOrderVO);
        }*/

        DeliveryPlan deliveryPlan = new DeliveryPlan();
        deliveryPlan.setOrderNo("123123");
        deliveryPlan.setDeliveryTime(LocalDate.now());
        deliveryPlan.setOrderStoreNo(123);
        String s = JSON.toJSONString(deliveryPlan);
        System.out.println(s);
        /*if (Objects.equals(null, InventoryTypeEnum.AGENT_PRODUCT.getType())){
            System.out.println("草");
        }*/
        /*BigDecimal bigDecimal = new BigDecimal(2);
        System.out.println(bigDecimal.setScale(2));*/

        /*String s2 = "[{\"afterSaleOrderStatus\":1,\"afterSaleQuantity\":0,\"afterSaleRemark\":\"阿斯顿\",\"afterSaleTime\":0,\"categoryId\":0,\"deliveryId\":5012194,\"deliveryed\":0,\"handleNum\":0,\"handleType\":11,\"orderNo\":\"02166071787982158\",\"quantity\":0,\"refundType\":\"停配/超区\",\"sku\":\"22805878104\",\"startTime\":\"2022-08-17T15:41:15.051\",\"suitId\":0,\"totalNumber\":0,\"type\":0}]";
        List<AfterSaleOrderVO> afterSaleOrderVO = JSON.parseObject(s2, List.class);
        System.out.println(afterSaleOrderVO);*/

       /* Integer sc = null;
        String s = "asd" +sc;
        System.out.println(s);*/

        /*BigDecimal bigDecimal = new BigDecimal(20);
        AjaxResult ok = AjaxResult.getOK(bigDecimal);
        System.out.println(JSON.toJSONString(ok));*/
    }


}
