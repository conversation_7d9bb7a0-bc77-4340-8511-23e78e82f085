package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.service.DeliveryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description: <br/>
 * date: 2022/9/7 14:21<br/>
 *
 * <AUTHOR> />
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class DeliveryServiceImplTest {

    @Autowired
    private DeliveryService deliveryService;

    @Test
    public void getCurrentDriverLocation(){
       /* System.out.println(deliveryService.getCurrentDriverLocation("01166252634894536", 1));
        System.out.println(deliveryService.getCurrentDriverLocation("01166252634894536", 2));*/
    }
}
