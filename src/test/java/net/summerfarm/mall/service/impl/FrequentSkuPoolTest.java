package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.service.AreaService;
import net.summerfarm.mall.service.FrequentSkuPoolService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

@Slf4j
@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@SpringBootTest
@RunWith(SpringRunner.class)
public class FrequentSkuPoolTest {

    @Resource
    private FrequentSkuPoolService frequentSkuPoolService;

    @Test
    public void test1() {
        Map<String, Boolean> stringBooleanMap = frequentSkuPoolService.checkSkuInFrequentSkuPool(352343L, Arrays.asList("858168732818"));
        log.info("stringBooleanMap = {}", JSON.toJSONString(stringBooleanMap));
    }
}
