package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.enums.ProductsRecommendEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ProductRecommendServiceImplTest {

    @Resource(name = "xmRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Test
    public void testPutListToRedis() {
        List<String> list = new ArrayList<>();
        list.add("test1");
        redisTemplate.opsForHash().put("test", "testing", list);
        Object o = redisTemplate.opsForHash().get("test", "testing");
        assertNotNull(o);
    }

    @Test
    public void testSerializer() {
        List<String> list = Arrays.asList("test1", "test2");
        redisTemplate.opsForHash().put("test", "testing", list);
        List<String> o = (List<String>) redisTemplate.opsForHash().get("test", "testing");
        assertNotNull(o);

        redisTemplate.opsForHash().put("test", "testing2", "list,of,strings");
        String o2 = (String) redisTemplate.opsForHash().get("test", "testing2");
        assertNotNull(o2);

        List<String> o3 = (List<String>) redisTemplate.opsForHash().get("this", "doesntexist");
        assertNull(o3);
    }

    @Test
    public void testString() {
        String x = null;
        List<String> split = Arrays.asList(x.split(","));
        System.out.println(split);
    }

    @Test
    public void testPutSku() {
        redisTemplate.opsForHash().put(ProductsRecommendEnum.HOME.getRedisKey(), "350883", Arrays.asList("2154357323457", "4831436836"));
    }

}