package net.summerfarm.mall.service.impl;

import com.github.pagehelper.PageInfo;
import net.summerfarm.BaseMvcTest;
import net.summerfarm.mall.enums.ProductsRecommendEnum;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.ProductRecommendService;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/1/24  10:35
 */
public class ProductRecommendServiceTest extends BaseMvcTest {
    @Override
    public Long loginMId() {
        return 14541L;
    }

    @Resource
    private ProductRecommendService productRecommendService;
    @Resource
    private RedisTemplate redisTemplate;

    @Test
    public void recommendSkuV2() {
        productRecommendService.recommendV2(1, 6, null, 0, null);
    }

    @Test
    public void addRecommendSku() {
        redisTemplate.opsForHash().put(ProductsRecommendEnum.HOME_V2.getRedisKey(), "1492",  "18101438437,908242663553,168211745786");

    }

    @Test
    public void detailPageRecommendV2() {
        PageInfo<ProductInfoVO> productInfoVOPageInfo = productRecommendService.recommendV2(1, 6, "5415253385", 3, null);
        System.out.println(productInfoVOPageInfo);
    }
}
