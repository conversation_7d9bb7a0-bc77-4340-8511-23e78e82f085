package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.service.facade.WncDeliveryAlertQueryFacade;
import net.summerfarm.mall.service.facade.dto.DeliveryAlertReq;
import net.summerfarm.mall.service.facade.dto.DeliveryAlertRes;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test cases for CompleteDeliveryServiceImpl.queryCompleteDeliveryTime method
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CompleteDeliveryService.queryCompleteDeliveryTime 综合测试")
class CompleteDeliveryServiceImplTest {

    @Mock
    private WncDeliveryAlertQueryFacade wncDeliveryAlertQueryFacade;

    @InjectMocks
    private CompleteDeliveryServiceImpl completeDeliveryService;

    private MockedStatic<RequestHolder> mockedRequestHolder;

    @BeforeEach
    void setUp() {
        mockedRequestHolder = mockStatic(RequestHolder.class);
    }

    @AfterEach
    void tearDown() {
        mockedRequestHolder.close();
    }

    // ========== 场景测试用例 (Scenario-based test cases) ==========

    @Test
    @DisplayName("场景1: 普通商户正常配送时间查询")
    void testQueryCompleteDeliveryTime_NormalMerchant_Success() {
        // Given: 普通商户的联系人信息
        Contact contact = createValidContact(12345L, 67890L, "杭州市", "余杭区", 101);
        boolean isMajor = false;
        LocalTime expectedTime = LocalTime.of(18, 30);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "配送完成时间不应为空");
        assertEquals(expectedTime, result, "应返回预期的配送完成时间");
        
        // Verify facade call
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req -> 
            req.getContactId().equals(67890L) &&
            req.getCity().equals("杭州市") &&
            req.getArea().equals("余杭区") &&
            req.getStoreNo().equals(101) &&
            req.getMerchantId().equals("12345") &&
            req.getAdminId() == null
        ));
    }

    @Test
    @DisplayName("场景2: 大客户配送时间查询 - 有AdminId")
    void testQueryCompleteDeliveryTime_MajorCustomer_WithAdminId() {
        // Given: 大客户联系人信息
        Contact contact = createValidContact(54321L, 98765L, "上海市", "浦东新区", 202);
        boolean isMajor = true;
        Integer adminId = 1507; // 大客户管理员ID
        LocalTime expectedTime = LocalTime.of(16, 0);

        mockedRequestHolder.when(RequestHolder::getAdminId).thenReturn(adminId);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "大客户配送完成时间不应为空");
        assertEquals(expectedTime, result, "应返回大客户的配送完成时间");
        
        // Verify facade call with adminId
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req -> 
            req.getContactId().equals(98765L) &&
            req.getCity().equals("上海市") &&
            req.getArea().equals("浦东新区") &&
            req.getStoreNo().equals(202) &&
            req.getMerchantId().equals("54321") &&
            req.getAdminId().equals("1507")
        ));
    }

    @Test
    @DisplayName("场景3: 大客户配送时间查询 - AdminId为空")
    void testQueryCompleteDeliveryTime_MajorCustomer_NullAdminId() {
        // Given: 大客户但AdminId为空的情况
        Contact contact = createValidContact(11111L, 22222L, "北京市", "朝阳区", 303);
        boolean isMajor = true;
        LocalTime expectedTime = LocalTime.of(20, 0);

        mockedRequestHolder.when(RequestHolder::getAdminId).thenReturn(null);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "配送完成时间不应为空");
        assertEquals(expectedTime, result, "应返回配送完成时间");
        
        // Verify adminId is null in request
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req -> 
            req.getAdminId() == null
        ));
    }

    @Test
    @DisplayName("场景4: 早配送时间场景 - 上午配送")
    void testQueryCompleteDeliveryTime_EarlyDelivery() {
        // Given: 早配送时间场景
        Contact contact = createValidContact(33333L, 44444L, "深圳市", "南山区", 404);
        boolean isMajor = false;
        LocalTime earlyTime = LocalTime.of(8, 30); // 早上8:30配送

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(earlyTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "早配送时间不应为空");
        assertEquals(earlyTime, result, "应返回早配送时间");
        assertTrue(result.isBefore(LocalTime.of(12, 0)), "应为上午配送时间");
    }

    @Test
    @DisplayName("场景5: 晚配送时间场景 - 夜间配送")
    void testQueryCompleteDeliveryTime_LateDelivery() {
        // Given: 晚配送时间场景
        Contact contact = createValidContact(55555L, 66666L, "广州市", "天河区", 505);
        boolean isMajor = false;
        LocalTime lateTime = LocalTime.of(22, 0); // 晚上10点配送

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(lateTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "晚配送时间不应为空");
        assertEquals(lateTime, result, "应返回晚配送时间");
        assertTrue(result.isAfter(LocalTime.of(18, 0)), "应为夜间配送时间");
    }

    // ========== 边界条件和异常测试用例 ==========

    @Test
    @DisplayName("边界条件1: Contact为null")
    void testQueryCompleteDeliveryTime_NullContact() {
        // Given: Contact为null
        Contact contact = null;
        boolean isMajor = false;

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNull(result, "Contact为null时应返回null");
        verifyNoInteractions(wncDeliveryAlertQueryFacade);
    }

    @Test
    @DisplayName("边界条件2: Contact的mId为null")
    void testQueryCompleteDeliveryTime_NullMerchantId() {
        // Given: Contact的mId为null
        Contact contact = new Contact();
        contact.setContactId(12345L);
        contact.setCity("杭州市");
        contact.setArea("余杭区");
        contact.setStoreNo(101);
        contact.setmId(null); // mId为null

        boolean isMajor = false;

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNull(result, "mId为null时应返回null");
        verifyNoInteractions(wncDeliveryAlertQueryFacade);
    }

    @Test
    @DisplayName("边界条件3: 外部服务返回null")
    void testQueryCompleteDeliveryTime_FacadeReturnsNull() {
        // Given: 外部服务返回null
        Contact contact = createValidContact(77777L, 88888L, "成都市", "锦江区", 606);
        boolean isMajor = false;

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(null);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNull(result, "外部服务返回null时应返回null");
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(any(DeliveryAlertReq.class));
    }

    @Test
    @DisplayName("边界条件4: DeliveryAlertRes的lastDeliveryTime为null")
    void testQueryCompleteDeliveryTime_ResponseWithNullTime() {
        // Given: 外部服务返回的响应中lastDeliveryTime为null
        Contact contact = createValidContact(99999L, 11111L, "武汉市", "武昌区", 707);
        boolean isMajor = false;

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(null); // 时间为null

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNull(result, "响应中时间为null时应返回null");
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(any(DeliveryAlertReq.class));
    }

    @Test
    @DisplayName("边界条件5: 极端时间值 - 午夜时间")
    void testQueryCompleteDeliveryTime_MidnightTime() {
        // Given: 午夜配送时间
        Contact contact = createValidContact(12121L, 21212L, "西安市", "雁塔区", 808);
        boolean isMajor = false;
        LocalTime midnightTime = LocalTime.of(0, 0); // 午夜12点

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(midnightTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "午夜时间不应为空");
        assertEquals(midnightTime, result, "应返回午夜时间");
        assertEquals(0, result.getHour(), "小时应为0");
        assertEquals(0, result.getMinute(), "分钟应为0");
    }

    @Test
    @DisplayName("边界条件6: 极端时间值 - 23:59时间")
    void testQueryCompleteDeliveryTime_LastMinuteOfDay() {
        // Given: 一天中最后一分钟的配送时间
        Contact contact = createValidContact(13131L, 31313L, "南京市", "鼓楼区", 909);
        boolean isMajor = false;
        LocalTime lastMinute = LocalTime.of(23, 59); // 23:59

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(lastMinute);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "23:59时间不应为空");
        assertEquals(lastMinute, result, "应返回23:59时间");
        assertEquals(23, result.getHour(), "小时应为23");
        assertEquals(59, result.getMinute(), "分钟应为59");
    }

    // ========== API/接口测试用例 ==========

    @Test
    @DisplayName("接口测试1: 参数验证 - Contact字段完整性")
    void testQueryCompleteDeliveryTime_ContactFieldValidation() {
        // Given: 测试Contact各字段是否正确传递
        Contact contact = new Contact();
        contact.setmId(98765L);
        contact.setContactId(56789L);
        contact.setCity("测试城市");
        contact.setArea("测试区域");
        contact.setStoreNo(999);

        boolean isMajor = false;
        LocalTime expectedTime = LocalTime.of(15, 30);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "结果不应为空");
        assertEquals(expectedTime, result, "应返回预期时间");

        // 验证所有字段都正确传递
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req -> {
            assertEquals(Long.valueOf(56789L), req.getContactId(), "ContactId应正确传递");
            assertEquals("测试城市", req.getCity(), "City应正确传递");
            assertEquals("测试区域", req.getArea(), "Area应正确传递");
            assertEquals(Integer.valueOf(999), req.getStoreNo(), "StoreNo应正确传递");
            assertEquals("98765", req.getMerchantId(), "MerchantId应正确传递");
            return true;
        }));
    }

    @Test
    @DisplayName("接口测试2: 返回值类型验证")
    void testQueryCompleteDeliveryTime_ReturnTypeValidation() {
        // Given: 验证返回值类型
        Contact contact = createValidContact(11111L, 22222L, "杭州市", "西湖区", 101);
        boolean isMajor = false;
        LocalTime expectedTime = LocalTime.of(14, 45, 30); // 包含秒数

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "返回值不应为null");
        assertInstanceOf(LocalTime.class, result, "返回值应为LocalTime类型");
        assertEquals(expectedTime, result, "应返回完整的时间信息包括秒数");
        assertEquals(14, result.getHour(), "小时应正确");
        assertEquals(45, result.getMinute(), "分钟应正确");
        assertEquals(30, result.getSecond(), "秒数应正确");
    }

    @Test
    @DisplayName("接口测试3: 异常处理 - 外部服务异常")
    void testQueryCompleteDeliveryTime_ExternalServiceException() {
        // Given: 外部服务抛出异常
        Contact contact = createValidContact(44444L, 55555L, "天津市", "和平区", 202);
        boolean isMajor = false;

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenThrow(new RuntimeException("外部服务异常"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);
        }, "应抛出外部服务异常");

        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(any(DeliveryAlertReq.class));
    }

    @Test
    @DisplayName("接口测试4: 方法签名合规性验证")
    void testQueryCompleteDeliveryTime_MethodSignatureCompliance() {
        // Given: 验证方法签名
        Contact contact = createValidContact(66666L, 77777L, "重庆市", "渝中区", 303);
        boolean isMajor = true;
        LocalTime expectedTime = LocalTime.of(17, 15);

        mockedRequestHolder.when(RequestHolder::getAdminId).thenReturn(1026);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When: 调用方法验证签名
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then: 验证方法能正常调用且返回正确类型
        assertNotNull(result, "方法应正常返回结果");
        assertEquals(expectedTime, result, "返回值应符合预期");

        // 验证方法参数类型正确
        assertDoesNotThrow(() -> {
            completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);
        }, "方法签名应符合接口定义");
    }

    // ========== 业务场景组合测试 ==========

    @Test
    @DisplayName("业务场景1: 喜茶大客户配送时间查询")
    void testQueryCompleteDeliveryTime_XichaMajorCustomer() {
        // Given: 喜茶大客户场景 (使用常量中的大客户ID)
        Contact contact = createValidContact(88888L, 99999L, "深圳市", "福田区", 404);
        boolean isMajor = true;
        Integer xichaAdminId = 1507; // 正式环境喜茶大客户ID
        LocalTime xichaDeliveryTime = LocalTime.of(16, 30);

        mockedRequestHolder.when(RequestHolder::getAdminId).thenReturn(xichaAdminId);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(xichaDeliveryTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "喜茶大客户配送时间不应为空");
        assertEquals(xichaDeliveryTime, result, "应返回喜茶大客户配送时间");

        // 验证大客户请求参数
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req ->
            req.getAdminId().equals("1507") &&
            req.getMerchantId().equals("88888")
        ));
    }

    @Test
    @DisplayName("业务场景2: 测试环境大客户配送时间查询")
    void testQueryCompleteDeliveryTime_TestEnvironmentMajorCustomer() {
        // Given: 测试环境大客户场景
        Contact contact = createValidContact(12345L, 54321L, "杭州市", "滨江区", 505);
        boolean isMajor = true;
        Integer testAdminId = 1026; // 测试环境鲜沐科技大客户ID
        LocalTime testDeliveryTime = LocalTime.of(19, 0);

        mockedRequestHolder.when(RequestHolder::getAdminId).thenReturn(testAdminId);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(testDeliveryTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "测试环境大客户配送时间不应为空");
        assertEquals(testDeliveryTime, result, "应返回测试环境大客户配送时间");

        // 验证测试环境大客户请求参数
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req ->
            req.getAdminId().equals("1026") &&
            req.getMerchantId().equals("12345")
        ));
    }

    @Test
    @DisplayName("业务场景3: 多城市配送时间差异验证")
    void testQueryCompleteDeliveryTime_MultiCityDeliveryTimes() {
        // Given: 不同城市的配送时间可能不同
        Contact beijingContact = createValidContact(11111L, 11111L, "北京市", "海淀区", 101);
        Contact shanghaiContact = createValidContact(22222L, 22222L, "上海市", "黄浦区", 202);

        LocalTime beijingTime = LocalTime.of(18, 0);
        LocalTime shanghaiTime = LocalTime.of(19, 30);

        // Mock北京配送时间
        DeliveryAlertRes beijingResponse = new DeliveryAlertRes();
        beijingResponse.setLastDeliveryTime(beijingTime);

        // Mock上海配送时间
        DeliveryAlertRes shanghaiResponse = new DeliveryAlertRes();
        shanghaiResponse.setLastDeliveryTime(shanghaiTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(argThat(req ->
            "北京市".equals(req.getCity()))))
            .thenReturn(beijingResponse);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(argThat(req ->
            "上海市".equals(req.getCity()))))
            .thenReturn(shanghaiResponse);

        // When
        LocalTime beijingResult = completeDeliveryService.queryCompleteDeliveryTime(beijingContact, false);
        LocalTime shanghaiResult = completeDeliveryService.queryCompleteDeliveryTime(shanghaiContact, false);

        // Then
        assertNotNull(beijingResult, "北京配送时间不应为空");
        assertNotNull(shanghaiResult, "上海配送时间不应为空");
        assertEquals(beijingTime, beijingResult, "应返回北京配送时间");
        assertEquals(shanghaiTime, shanghaiResult, "应返回上海配送时间");
        assertNotEquals(beijingResult, shanghaiResult, "不同城市配送时间应不同");
    }

    @Test
    @DisplayName("业务场景4: 同一商户多地址配送时间查询")
    void testQueryCompleteDeliveryTime_SameMerchantMultipleAddresses() {
        // Given: 同一商户的不同地址
        Long sameMerchantId = 99999L;
        Contact homeAddress = createValidContact(sameMerchantId, 11111L, "杭州市", "西湖区", 101);
        Contact officeAddress = createValidContact(sameMerchantId, 22222L, "杭州市", "滨江区", 102);

        LocalTime homeTime = LocalTime.of(18, 0);
        LocalTime officeTime = LocalTime.of(20, 0);

        // Mock不同地址的配送时间
        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(argThat(req ->
            req.getContactId().equals(11111L))))
            .thenReturn(createMockResponse(homeTime));

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(argThat(req ->
            req.getContactId().equals(22222L))))
            .thenReturn(createMockResponse(officeTime));

        // When
        LocalTime homeResult = completeDeliveryService.queryCompleteDeliveryTime(homeAddress, false);
        LocalTime officeResult = completeDeliveryService.queryCompleteDeliveryTime(officeAddress, false);

        // Then
        assertNotNull(homeResult, "家庭地址配送时间不应为空");
        assertNotNull(officeResult, "办公地址配送时间不应为空");
        assertEquals(homeTime, homeResult, "应返回家庭地址配送时间");
        assertEquals(officeTime, officeResult, "应返回办公地址配送时间");

        // 验证两次调用都使用了相同的商户ID
        verify(wncDeliveryAlertQueryFacade, times(2)).queryDeliveryAlert(argThat(req ->
            req.getMerchantId().equals(sameMerchantId.toString())
        ));
    }

    // ========== 数据完整性和边界测试 ==========

    @Test
    @DisplayName("数据完整性1: Contact字段为空字符串")
    void testQueryCompleteDeliveryTime_EmptyStringFields() {
        // Given: Contact字段为空字符串
        Contact contact = new Contact();
        contact.setmId(12345L);
        contact.setContactId(67890L);
        contact.setCity(""); // 空字符串
        contact.setArea(""); // 空字符串
        contact.setStoreNo(101);

        boolean isMajor = false;
        LocalTime expectedTime = LocalTime.of(18, 0);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "即使城市区域为空字符串也应返回结果");
        assertEquals(expectedTime, result, "应返回预期时间");

        // 验证空字符串被正确传递
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req ->
            "".equals(req.getCity()) && "".equals(req.getArea())
        ));
    }

    @Test
    @DisplayName("数据完整性2: StoreNo为null")
    void testQueryCompleteDeliveryTime_NullStoreNo() {
        // Given: StoreNo为null
        Contact contact = createValidContact(11111L, 22222L, "南京市", "玄武区", null);
        boolean isMajor = false;
        LocalTime expectedTime = LocalTime.of(17, 30);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When
        LocalTime result = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then
        assertNotNull(result, "StoreNo为null时应正常返回结果");
        assertEquals(expectedTime, result, "应返回预期时间");

        // 验证null值被正确传递
        verify(wncDeliveryAlertQueryFacade).queryDeliveryAlert(argThat(req ->
            req.getStoreNo() == null
        ));
    }

    @Test
    @DisplayName("性能测试: 多次调用一致性验证")
    void testQueryCompleteDeliveryTime_MultipleCallsConsistency() {
        // Given: 相同参数多次调用
        Contact contact = createValidContact(55555L, 66666L, "苏州市", "姑苏区", 606);
        boolean isMajor = false;
        LocalTime expectedTime = LocalTime.of(19, 45);

        DeliveryAlertRes mockResponse = new DeliveryAlertRes();
        mockResponse.setLastDeliveryTime(expectedTime);

        when(wncDeliveryAlertQueryFacade.queryDeliveryAlert(any(DeliveryAlertReq.class)))
                .thenReturn(mockResponse);

        // When: 多次调用
        LocalTime result1 = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);
        LocalTime result2 = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);
        LocalTime result3 = completeDeliveryService.queryCompleteDeliveryTime(contact, isMajor);

        // Then: 验证结果一致性
        assertNotNull(result1, "第一次调用结果不应为空");
        assertNotNull(result2, "第二次调用结果不应为空");
        assertNotNull(result3, "第三次调用结果不应为空");

        assertEquals(result1, result2, "多次调用结果应一致");
        assertEquals(result2, result3, "多次调用结果应一致");
        assertEquals(expectedTime, result1, "应返回预期时间");

        // 验证外部服务被调用了3次
        verify(wncDeliveryAlertQueryFacade, times(3)).queryDeliveryAlert(any(DeliveryAlertReq.class));
    }

    // ========== 辅助方法 ==========

    /**
     * 创建有效的Contact对象
     */
    private Contact createValidContact(Long mId, Long contactId, String city, String area, Integer storeNo) {
        Contact contact = new Contact();
        contact.setmId(mId);
        contact.setContactId(contactId);
        contact.setCity(city);
        contact.setArea(area);
        contact.setStoreNo(storeNo);
        contact.setContact("测试联系人");
        contact.setPhone("13800138000");
        contact.setAddress("测试地址123号");
        return contact;
    }

    /**
     * 创建Mock响应对象
     */
    private DeliveryAlertRes createMockResponse(LocalTime time) {
        DeliveryAlertRes response = new DeliveryAlertRes();
        response.setLastDeliveryTime(time);
        return response;
    }
}
