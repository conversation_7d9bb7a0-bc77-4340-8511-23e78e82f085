package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.service.OrderCalcService;
import net.summerfarm.mall.service.helper.OrderCalcTestDataBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OrderCalcService 单元测试类
 * 测试到手价计算功能，特别是新增的用户上下文参数方法
 * 
 * <AUTHOR>
 */
@SpringBootTest
@SpringJUnitConfig
@ExtendWith(MockitoExtension.class)
@DisplayName("订单计算服务测试")
public class OrderCalcServiceImplTest {

    @Resource
    private OrderCalcService orderCalcService;

    private PlaceOrderVO basicOrderVO;
    private UserContextParam singleStoreUser;
    private UserContextParam majorMerchantUser;
    private UserContextParam popMerchantUser;

    @BeforeEach
    void setUp() {
        // 准备基础测试数据
        basicOrderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();
        singleStoreUser = OrderCalcTestDataBuilder.createSingleStoreUserContext();
        majorMerchantUser = OrderCalcTestDataBuilder.createMajorMerchantUserContext();
        popMerchantUser = OrderCalcTestDataBuilder.createPopMerchantUserContext();
    }

    @Test
    @DisplayName("测试单店用户到手价计算")
    void testTakePriceHandlerForSingleStore() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "到手价结果不应为空");
        // 由于是单店用户，应该返回到手价信息
        // 具体的断言需要根据实际的业务逻辑和测试数据来调整
    }

    @Test
    @DisplayName("测试大客户不返回到手价信息")
    void testTakePriceHandlerForMajorMerchant() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createMajorMerchantUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "结果不应为空");
        assertTrue(result.isEmpty(), "大客户应该返回空的到手价列表");
    }

    @Test
    @DisplayName("测试POP商城用户到手价计算")
    void testTakePriceHandlerForPopMerchant() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createPopMerchantUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "到手价结果不应为空");
        // POP商城用户应该能正常计算到手价
    }

    @Test
    @DisplayName("测试空订单处理")
    void testTakePriceHandlerWithEmptyOrder() {
        // Given
        PlaceOrderVO emptyOrderVO = new PlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(emptyOrderVO, userContext);

        // Then
        assertNotNull(result, "结果不应为空");
        assertTrue(result.isEmpty(), "空订单应该返回空列表");
    }

    @Test
    @DisplayName("测试空用户上下文处理")
    void testTakePriceHandlerWithNullUserContext() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            orderCalcService.takePriceHandler(orderVO, null);
        }, "空用户上下文应该抛出异常");
    }

    @Test
    @DisplayName("测试省心送到手价计算")
    void testTakePriceHandlerForTimingOrder() {
        // Given
        PlaceOrderVO timingOrderVO = OrderCalcTestDataBuilder.createTimingPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(timingOrderVO, userContext);

        // Then
        assertNotNull(result, "省心送到手价结果不应为空");
        // 省心送的具体断言需要根据业务逻辑调整
    }

    @Test
    @DisplayName("测试代下单用户到手价计算")
    void testTakePriceHandlerForHelpOrder() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createHelpOrderUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "代下单到手价结果不应为空");
        // 验证代下单逻辑是否正确处理
    }

    @Test
    @DisplayName("测试特定SKU到手价计算")
    void testTakePriceHandlerForSpecificSku() {
        // Given
        String testSku = "TEST_SPECIFIC_SKU_001";
        Integer quantity = 5;
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(testSku, quantity);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "特定SKU到手价结果不应为空");
        
        // 如果有返回结果，验证SKU和数量是否正确
        if (!result.isEmpty()) {
            TakeActualPriceVO priceVO = result.get(0);
            assertEquals(testSku, priceVO.getSku(), "SKU应该匹配");
            assertEquals(quantity, priceVO.getQuantity(), "数量应该匹配");
            assertNotNull(priceVO.getTakeActualPrice(), "到手价不应为空");
            assertTrue(priceVO.getTakeActualPrice().compareTo(BigDecimal.ZERO) >= 0, "到手价应该大于等于0");
        }
    }

    @Test
    @DisplayName("测试混合商品类型订单")
    void testTakePriceHandlerForMixedProducts() {
        // Given
        PlaceOrderVO mixedOrderVO = OrderCalcTestDataBuilder.createMixedProductOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(mixedOrderVO, userContext);

        // Then
        assertNotNull(result, "混合商品订单结果不应为空");
        // 验证不同商品类型的处理逻辑
    }

    @Test
    @DisplayName("测试用户上下文参数完整性")
    void testUserContextParamIntegrity() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // 验证用户上下文参数的完整性
        assertNotNull(userContext.getMId(), "商户ID不应为空");
        assertNotNull(userContext.getAccountId(), "账户ID不应为空");
        assertNotNull(userContext.getMname(), "商户名称不应为空");
        assertNotNull(userContext.getMajorMerchant(), "大客户标识不应为空");
        assertNotNull(userContext.getArea(), "区域信息不应为空");

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "结果不应为空");
    }

    @Test
    @DisplayName("测试不同业务线用户")
    void testDifferentBusinessLineUsers() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();

        // 鲜沐用户
        UserContextParam xianmuUser = OrderCalcTestDataBuilder.createSingleStoreUserContext();
        xianmuUser.setBusinessLine(0);

        // POP用户
        UserContextParam popUser = OrderCalcTestDataBuilder.createPopMerchantUserContext();
        popUser.setBusinessLine(1);

        // When
        List<TakeActualPriceVO> xianmuResult = orderCalcService.takePriceHandler(orderVO, xianmuUser);
        List<TakeActualPriceVO> popResult = orderCalcService.takePriceHandler(orderVO, popUser);

        // Then
        assertNotNull(xianmuResult, "鲜沐用户结果不应为空");
        assertNotNull(popResult, "POP用户结果不应为空");

        // 可以根据业务需求添加更具体的断言
        // 比如不同业务线的价格计算逻辑差异
    }

    @Test
    @DisplayName("测试原有方法兼容性")
    void testOriginalMethodCompatibility() {
        // 这个测试需要在有RequestHolder上下文的情况下运行
        // 主要验证原有方法仍然可以正常工作

        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();

        // When & Then
        // 由于原有方法依赖RequestHolder，在单元测试环境下可能会失败
        // 这里主要是验证方法签名的兼容性
        assertDoesNotThrow(() -> {
            // 在实际测试中，可能需要mock RequestHolder
            // List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO);
        }, "原有方法应该保持兼容性");
    }

    @Test
    @DisplayName("测试边界条件 - 极大数量")
    void testBoundaryConditionLargeQuantity() {
        // Given
        String testSku = "BOUNDARY_TEST_SKU";
        Integer largeQuantity = 999999;
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(testSku, largeQuantity);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "大数量订单结果不应为空");

        if (!result.isEmpty()) {
            TakeActualPriceVO priceVO = result.get(0);
            assertEquals(largeQuantity, priceVO.getQuantity(), "数量应该正确处理");
            assertNotNull(priceVO.getTakeActualPrice(), "到手价应该能正确计算");
        }
    }

    @Test
    @DisplayName("测试边界条件 - 最小数量")
    void testBoundaryConditionMinQuantity() {
        // Given
        String testSku = "MIN_QUANTITY_SKU";
        Integer minQuantity = 1;
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(testSku, minQuantity);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "最小数量订单结果不应为空");

        if (!result.isEmpty()) {
            TakeActualPriceVO priceVO = result.get(0);
            assertEquals(minQuantity, priceVO.getQuantity(), "最小数量应该正确处理");
        }
    }

    @Test
    @DisplayName("测试性能 - 大批量SKU处理")
    void testPerformanceLargeBatchSku() {
        // Given
        int skuCount = 100; // 100个SKU
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createLargeBatchOrderVO(skuCount);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        long startTime = System.currentTimeMillis();
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);
        long endTime = System.currentTimeMillis();

        // Then
        assertNotNull(result, "大批量SKU结果不应为空");

        long executionTime = endTime - startTime;
        System.out.println("处理" + skuCount + "个SKU耗时: " + executionTime + "ms");

        // 性能断言 - 可以根据实际需求调整时间阈值
        assertTrue(executionTime < 10000, "处理时间应该在合理范围内（小于10秒）");
    }

    @Test
    @DisplayName("测试用户上下文参数验证")
    void testUserContextParamValidation() {
        // Given
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createBasicPlaceOrderVO();

        // 测试各种不完整的用户上下文
        UserContextParam incompleteContext = new UserContextParam();
        incompleteContext.setMId(1000L);
        // 故意不设置其他必要字段

        // When & Then
        assertDoesNotThrow(() -> {
            List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, incompleteContext);
            assertNotNull(result, "即使用户上下文不完整，也应该返回结果");
        }, "不完整的用户上下文应该能够优雅处理");
    }
}
