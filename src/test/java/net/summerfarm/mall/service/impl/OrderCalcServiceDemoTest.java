//package net.summerfarm.mall.service.impl;
//
//import net.summerfarm.mall.model.domain.Area;
//import net.summerfarm.mall.model.domain.Trolley;
//import net.summerfarm.mall.model.dto.order.UserContextParam;
//import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
//import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
//import net.summerfarm.mall.service.OrderCalcService;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * OrderCalcService 演示测试
// * 展示新功能的使用方法
// *
// * <AUTHOR>
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class OrderCalcServiceDemoTest {
//
//    @Resource
//    private OrderCalcService orderCalcService;
//
//    @Test
//    public void demonstrateNewTakePriceMethod() {
//        System.out.println("=== OrderCalcService 新功能演示 ===");
//
//        // 1. 创建用户上下文参数
//        UserContextParam userContext = new UserContextParam();
//        userContext.setMId(12345L);
//        userContext.setAccountId(67890L);
//        userContext.setMname("演示用户店铺");
//        userContext.setMajorMerchant(false); // 单店用户
//        userContext.setDirect(2); // 现结
//        userContext.setSkuShow(2); // 全量
//        userContext.setServer(1); // 服务区内
//        userContext.setBusinessLine(0); // 鲜沐
//        userContext.setSize("单店");
//        userContext.setHelpOrder(0);
//        userContext.setOutTimes(0);
//        userContext.setOpenId("demo_openid");
//
//        // 设置区域信息
//        Area area = new Area();
//        area.setAreaNo(1001);
//        area.setAreaName("演示区域");
//        userContext.setArea(area);
//
//        // 2. 创建订单信息
//        PlaceOrderVO orderVO = new PlaceOrderVO();
//        orderVO.setTakePriceFlag(true); // 获取明细，不均摊优惠
//        orderVO.setIsTakePrice(0); // 到手价计算
//
//        // 添加要计算的SKU
//        List<Trolley> orderNow = new ArrayList<>();
//        Trolley trolley = new Trolley();
//        trolley.setSku("DEMO_SKU_001"); // 演示SKU
//        trolley.setQuantity(2);
//        trolley.setProductType(0);
//        trolley.setSuitId(0);
//        orderNow.add(trolley);
//        orderVO.setOrderNow(orderNow);
//
//        // 3. 调用新的到手价计算方法
//        try {
//            List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);
//
//            System.out.println("调用成功！");
//            System.out.println("用户信息：");
//            System.out.println("  商户ID: " + userContext.getMId());
//            System.out.println("  商户名称: " + userContext.getMname());
//            System.out.println("  是否大客户: " + userContext.getMajorMerchant());
//            System.out.println("  业务线: " + (userContext.getBusinessLine() == 0 ? "鲜沐" : "POP"));
//
//            System.out.println("计算结果：");
//            if (result != null) {
//                System.out.println("  结果数量: " + result.size());
//                for (TakeActualPriceVO priceVO : result) {
//                    System.out.println("  SKU: " + priceVO.getSku());
//                    System.out.println("  数量: " + priceVO.getQuantity());
//                    System.out.println("  原价: " + priceVO.getPrice());
//                    System.out.println("  到手价: " + priceVO.getTakeActualPrice());
//                    System.out.println("  ---");
//                }
//            } else {
//                System.out.println("  结果为空");
//            }
//
//        } catch (Exception e) {
//            System.out.println("调用出现异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void demonstrateMajorMerchantCase() {
//        System.out.println("=== 大客户场景演示 ===");
//
//        // 创建大客户上下文
//        UserContextParam userContext = new UserContextParam();
//        userContext.setMId(54321L);
//        userContext.setAccountId(98765L);
//        userContext.setMname("大客户演示");
//        userContext.setMajorMerchant(true); // 大客户
//        userContext.setAdminId(1001);
//        userContext.setDirect(1); // 账期
//        userContext.setSkuShow(1); // 定量
//        userContext.setServer(1); // 服务区内
//        userContext.setBusinessLine(0); // 鲜沐
//        userContext.setSize("大客户");
//
//        Area area = new Area();
//        area.setAreaNo(1001);
//        userContext.setArea(area);
//
//        // 创建订单
//        PlaceOrderVO orderVO = new PlaceOrderVO();
//        orderVO.setTakePriceFlag(true);
//        orderVO.setIsTakePrice(0);
//
//        List<Trolley> orderNow = new ArrayList<>();
//        Trolley trolley = new Trolley();
//        trolley.setSku("DEMO_SKU_002");
//        trolley.setQuantity(5);
//        trolley.setProductType(0);
//        trolley.setSuitId(0);
//        orderNow.add(trolley);
//        orderVO.setOrderNow(orderNow);
//
//        try {
//            List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);
//
//            System.out.println("大客户调用结果：");
//            System.out.println("  是否大客户: " + userContext.getMajorMerchant());
//            System.out.println("  结果数量: " + (result != null ? result.size() : "null"));
//
//            if (result != null && result.isEmpty()) {
//                System.out.println("  ✓ 符合预期：大客户不返回到手价信息");
//            } else {
//                System.out.println("  ✗ 意外：大客户返回了到手价信息");
//            }
//
//        } catch (Exception e) {
//            System.out.println("大客户调用出现异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void demonstrateMultipleSkus() {
//        System.out.println("=== 多SKU场景演示 ===");
//
//        // 创建用户上下文
//        UserContextParam userContext = UserContextParam.createDefaultSingleStore();
//        userContext.setMId(11111L);
//        userContext.setAccountId(22222L);
//        userContext.setMname("多SKU演示店铺");
//
//        // 创建包含多个SKU的订单
//        PlaceOrderVO orderVO = new PlaceOrderVO();
//        orderVO.setTakePriceFlag(true);
//        orderVO.setIsTakePrice(0);
//
//        List<Trolley> orderNow = new ArrayList<>();
//
//        // 添加多个SKU
//        String[] skus = {"DEMO_SKU_001", "DEMO_SKU_002", "DEMO_SKU_003"};
//        Integer[] quantities = {1, 2, 3};
//
//        for (int i = 0; i < skus.length; i++) {
//            Trolley trolley = new Trolley();
//            trolley.setSku(skus[i]);
//            trolley.setQuantity(quantities[i]);
//            trolley.setProductType(0);
//            trolley.setSuitId(0);
//            orderNow.add(trolley);
//        }
//        orderVO.setOrderNow(orderNow);
//
//        try {
//            List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);
//
//            System.out.println("多SKU调用结果：");
//            System.out.println("  输入SKU数量: " + skus.length);
//            System.out.println("  输出结果数量: " + (result != null ? result.size() : "null"));
//
//            if (result != null) {
//                for (TakeActualPriceVO priceVO : result) {
//                    System.out.println("  SKU: " + priceVO.getSku() +
//                                     ", 数量: " + priceVO.getQuantity() +
//                                     ", 到手价: " + priceVO.getTakeActualPrice());
//                }
//            }
//
//        } catch (Exception e) {
//            System.out.println("多SKU调用出现异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void demonstrateMethodComparison() {
//        System.out.println("=== 新旧方法对比演示 ===");
//
//        // 创建订单
//        PlaceOrderVO orderVO = new PlaceOrderVO();
//        orderVO.setTakePriceFlag(true);
//        orderVO.setIsTakePrice(0);
//
//        List<Trolley> orderNow = new ArrayList<>();
//        Trolley trolley = new Trolley();
//        trolley.setSku("DEMO_SKU_001");
//        trolley.setQuantity(1);
//        trolley.setProductType(0);
//        trolley.setSuitId(0);
//        orderNow.add(trolley);
//        orderVO.setOrderNow(orderNow);
//
//        // 创建用户上下文
//        UserContextParam userContext = UserContextParam.createDefaultSingleStore();
//        userContext.setMId(99999L);
//        userContext.setAccountId(88888L);
//
//        try {
//            // 调用新方法（带用户上下文参数）
//            System.out.println("调用新方法（带用户上下文参数）：");
//            List<TakeActualPriceVO> newResult = orderCalcService.takePriceHandler(orderVO, userContext);
//            System.out.println("  新方法结果数量: " + (newResult != null ? newResult.size() : "null"));
//
//            // 注意：原有方法依赖RequestHolder，在单元测试环境下可能无法正常工作
//            System.out.println("原有方法依赖RequestHolder，在单元测试环境下需要特殊处理");
//
//            System.out.println("✓ 新方法的优势：");
//            System.out.println("  - 不依赖RequestHolder，可以独立测试");
//            System.out.println("  - 可以模拟任意用户的场景");
//            System.out.println("  - 便于编写单元测试");
//            System.out.println("  - 保持了原有方法的兼容性");
//
//        } catch (Exception e) {
//            System.out.println("方法对比出现异常: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//}
