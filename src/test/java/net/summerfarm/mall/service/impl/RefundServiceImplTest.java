package net.summerfarm.mall.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.HandleEventStatus;
import net.summerfarm.enums.RefundReponseEnum;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.mapper.ConfigMapper;
import net.summerfarm.mall.mapper.RefundHandleEventMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.Config;
import net.summerfarm.mall.model.domain.Refund;
import net.summerfarm.mall.model.domain.RefundHandleEvent;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.service.ConfigService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Collections;

import static net.summerfarm.enums.RefundReponseEnum.*;

public class RefundServiceImplTest {
    private RefundServiceImpl refundService = new RefundServiceImpl();

    @Test
    public void refundRealInhandling(){
        RefundHandleEventMapper refundHandleEventMapper = Mockito.mock(RefundHandleEventMapper.class);
        refundService.setRefundHandleEventMapper(refundHandleEventMapper);
        RefundMapper refundMapper = Mockito.mock(RefundMapper.class);
        refundService.setRefundMapper(refundMapper);
        PaymentHandler paymentHandler = Mockito.mock(PaymentHandler.class);
        refundService.setPaymentHandler(paymentHandler);
        ConfigService configMapper = Mockito.mock(ConfigService.class);
        refundService.setConfigService(configMapper);

        RefundHandleEvent event = new RefundHandleEvent();
        event.setRefundNo("1234");
        event.setRetryCount(1);
        event.setId(3);
        event.setStatus(HandleEventStatus.IN_HANDLING.ordinal());

        Refund refund = new Refund();
        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());

        Mockito.when(refundMapper.selectByRefundNo(Mockito.anyString())).thenReturn(refund);
        Mockito.when(refundHandleEventMapper.selectAllByStatus(Mockito.any())).thenReturn(Collections.singletonList(event));
        Mockito.when(paymentHandler.queryRefund(Mockito.anyString())).thenReturn(AjaxResult.getOK());
        // 反查为S,更新为已完成
        refundService.refundReal();
        Mockito.verify(paymentHandler).queryRefund(Mockito.any());
        Mockito.verify(refundHandleEventMapper).deleteByPrimaryKey(Mockito.any());
        Assert.assertEquals(RefundStatusEnum.SUCCESS.ordinal(), (int)refund.getStatus());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", P));
        // 反查为P,不处理
        refundService.refundReal();
        Mockito.verify(refundHandleEventMapper, Mockito.atMostOnce()).deleteByPrimaryKey(Mockito.any());
        Assert.assertEquals(RefundStatusEnum.IN_HANLING.ordinal(), (int)refund.getStatus());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        event.setRetryCount(6);
        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", F));
        // 反差为F,重试次数达到,报错,更新为FAIL
        refundService.refundReal();
        Assert.assertEquals(RefundStatusEnum.FAIL.ordinal(), (int)refund.getStatus());
        // 验证更新重试次数
        Mockito.verify(refundHandleEventMapper, Mockito.times(1))
                .updateByPrimaryKeySelective(Mockito.any());
        // 验证更新退款
        Mockito.verify(refundHandleEventMapper).updateStatusByRefundNo(Mockito.any(), Mockito.any());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        event.setRetryCount(5);
        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", F));
        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK());
        // 反查为F,重试,成功,更新退款为成功
        refundService.refundReal();
        // 验证更新为成功
        Assert.assertEquals(RefundStatusEnum.SUCCESS.ordinal(), (int)refund.getStatus());
        // 验证更新重试次数
        Mockito.verify(refundHandleEventMapper, Mockito.times(2))
                .updateByPrimaryKeySelective(Mockito.any());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        event.setRetryCount(5);
        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", F));
        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", P));
        // 反查为F,重试,为P,不处理退款
        refundService.refundReal();
        // 验证未更新
        Assert.assertEquals(RefundStatusEnum.IN_HANLING.ordinal(), (int)refund.getStatus());
        // 验证更新重试次数
        Mockito.verify(refundHandleEventMapper, Mockito.times(3)).updateByPrimaryKeySelective(Mockito.any());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        event.setRetryCount(4);
        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", F));
        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", F));
        // 反查为F,重试,为P,不处理退款
        refundService.refundReal();
        // 验证未更新
        Assert.assertEquals(RefundStatusEnum.IN_HANLING.ordinal(), (int)refund.getStatus());
        // 验证更新重试次数
        Mockito.verify(refundHandleEventMapper, Mockito.times(4)).updateByPrimaryKeySelective(Mockito.any());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        event.setRetryCount(4);
        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK("", F));
        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenThrow(new RuntimeException());
        // 反查为F,重试失败,不处理退款
        refundService.refundReal();
        // 验证未更新
        Assert.assertEquals(RefundStatusEnum.IN_HANLING.ordinal(), (int)refund.getStatus());
        // 验证更新重试次数
        Mockito.verify(refundHandleEventMapper, Mockito.times(5)).updateByPrimaryKeySelective(Mockito.any());
    }

    @Test
    public void refundRealNew() {
        RefundHandleEventMapper refundHandleEventMapper = Mockito.mock(RefundHandleEventMapper.class);
        refundService.setRefundHandleEventMapper(refundHandleEventMapper);
        RefundMapper refundMapper = Mockito.mock(RefundMapper.class);
        refundService.setRefundMapper(refundMapper);
        PaymentHandler paymentHandler = Mockito.mock(PaymentHandler.class);
        refundService.setPaymentHandler(paymentHandler);
        ConfigService configMapper = Mockito.mock(ConfigService.class);
        /*Config config = new Config();
        config.setValue("https://oapi.dingtalk.com/robot/send?access_token=fca40dcc662f0858eddec91d975a374546a800db0fdd498c7676e8d01f08ba9a");
        Mockito.when(configMapper.getValue(Mockito.eq(RefundServiceImpl.CHECK_BILL_ROBOT_URL)))
                        .thenReturn(config);*/
        refundService.setConfigService(configMapper);

        RefundHandleEvent event = new RefundHandleEvent();
        event.setRefundNo("1234");
        event.setRetryCount(1);
        event.setId(3);
        event.setStatus(HandleEventStatus.NEW.ordinal());

        Refund refund = new Refund();
        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        refund.setRefundNo("01231238392");
        refund.setAfterSaleOrderNo("12391231832");
        refund.setRefundFee(BigDecimal.valueOf(12300));

        Mockito.when(refundMapper.selectByRefundNo(Mockito.anyString())).thenReturn(refund);
        Mockito.when(refundHandleEventMapper.selectAllByStatus(Mockito.any())).thenReturn(Collections.singletonList(event));
        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK())
                        .thenThrow(new RuntimeException()).thenThrow(new RuntimeException());

        refundService.refundReal();
        Mockito.verify(paymentHandler).performRefund(Mockito.any());
        Mockito.verify(refundHandleEventMapper).deleteByPrimaryKey(Mockito.any());
        Assert.assertEquals(RefundStatusEnum.SUCCESS.ordinal(), (int)refund.getStatus());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        refundService.refundReal();
        Assert.assertEquals(RefundStatusEnum.IN_HANLING.ordinal(), (int)refund.getStatus());
        Mockito.verify(refundHandleEventMapper, Mockito.atMostOnce()).deleteByPrimaryKey(Mockito.any());

        refund.setStatus((byte) RefundStatusEnum.IN_HANLING.ordinal());
        event.setRetryCount(100);
        refundService.refundReal();
        Assert.assertEquals(RefundStatusEnum.FAIL.ordinal(), (int)refund.getStatus());

    }

    @Test
    public void finish() {
        RefundHandleEventMapper refundHandleEventMapper = Mockito.mock(RefundHandleEventMapper.class);
        refundService.setRefundHandleEventMapper(refundHandleEventMapper);
        RefundMapper refundMapper = Mockito.mock(RefundMapper.class);
        refundService.setRefundMapper(refundMapper);

        refundService.finish(new Refund(), new RefundHandleEvent());

        Mockito.verify(refundHandleEventMapper).deleteByPrimaryKey(Mockito.any());
        Mockito.verify(refundMapper).updateByPrimaryKeySelective(Mockito.any());
    }

    @Test
    public void performRefund() {
        PaymentHandler paymentHandler = Mockito.mock(PaymentHandler.class);
        refundService.setPaymentHandler(paymentHandler);

        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getError());
        RefundReponseEnum status = refundService.performRefund(new Refund());
        Assert.assertEquals(F, status);

        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK());
        status = refundService.performRefund(new Refund());
        Assert.assertEquals(S, status);

        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK(S));
        status = refundService.performRefund(new Refund());
        Assert.assertEquals(S, status);

        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK(P));
        status = refundService.performRefund(new Refund());
        Assert.assertEquals(P, status);

        Mockito.when(paymentHandler.performRefund(Mockito.any())).thenReturn(AjaxResult.getOK(F));
        status = refundService.performRefund(new Refund());
        Assert.assertEquals(F, status);
    }

    @Test
    public void lookup(){
        PaymentHandler paymentHandler = Mockito.mock(PaymentHandler.class);
        refundService.setPaymentHandler(paymentHandler);

        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK());
        RefundReponseEnum status = refundService.lookup(new RefundHandleEvent());
        Assert.assertEquals(S, status);

        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getOK(P));
        status = refundService.lookup(new RefundHandleEvent());
        Assert.assertEquals(P, status);

        Mockito.when(paymentHandler.queryRefund(Mockito.any())).thenReturn(AjaxResult.getError());
        status = refundService.lookup(new RefundHandleEvent());
        Assert.assertEquals(F, status);
    }
}