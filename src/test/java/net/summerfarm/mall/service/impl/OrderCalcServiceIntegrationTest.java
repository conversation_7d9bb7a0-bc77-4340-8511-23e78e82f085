package net.summerfarm.mall.service.impl;

import net.summerfarm.mall.model.dto.order.UserContextParam;
import net.summerfarm.mall.model.vo.order.PlaceOrderVO;
import net.summerfarm.mall.model.vo.price.TakeActualPriceVO;
import net.summerfarm.mall.service.OrderCalcService;
import net.summerfarm.mall.service.helper.OrderCalcTestDataBuilder;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OrderCalcService 集成测试类
 * 测试真实业务场景下的到手价计算功能
 * 
 * <AUTHOR>
 */
@SpringBootTest
@SpringJUnitConfig
@DisplayName("订单计算服务集成测试")
public class OrderCalcServiceIntegrationTest {

    @Resource
    private OrderCalcService orderCalcService;

    @Test
    @DisplayName("集成测试 - 真实SKU到手价计算")
    void testRealSkuTakePriceCalculation() {
        // Given - 使用真实存在的SKU进行测试
        String realSku = "863080734120"; // 这应该是系统中真实存在的SKU
        Integer quantity = 2;
        
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(realSku, quantity);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then
        assertNotNull(result, "真实SKU到手价结果不应为空");
        
        if (!result.isEmpty()) {
            TakeActualPriceVO priceVO = result.get(0);
            assertEquals(realSku, priceVO.getSku(), "SKU应该匹配");
            assertEquals(quantity, priceVO.getQuantity(), "数量应该匹配");
            
            // 验证价格相关字段
            assertNotNull(priceVO.getPrice(), "原价不应为空");
            assertNotNull(priceVO.getTakeActualPrice(), "到手价不应为空");
            
            // 业务逻辑验证
            assertTrue(priceVO.getPrice().compareTo(BigDecimal.ZERO) > 0, "原价应该大于0");
            assertTrue(priceVO.getTakeActualPrice().compareTo(BigDecimal.ZERO) > 0, "到手价应该大于0");
            
            // 到手价应该小于等于原价（考虑优惠）
            assertTrue(priceVO.getTakeActualPrice().compareTo(priceVO.getPrice()) <= 0, 
                      "到手价应该小于等于原价");
            
            System.out.println("SKU: " + priceVO.getSku());
            System.out.println("原价: " + priceVO.getPrice());
            System.out.println("到手价: " + priceVO.getTakeActualPrice());
            System.out.println("数量: " + priceVO.getQuantity());
        }
    }

    @Test
    @DisplayName("集成测试 - 多用户类型对比")
    void testMultipleUserTypesComparison() {
        // Given
        String testSku = "863080734120";
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(testSku, 1);
        
        UserContextParam singleStoreUser = OrderCalcTestDataBuilder.createSingleStoreUserContext();
        UserContextParam majorMerchantUser = OrderCalcTestDataBuilder.createMajorMerchantUserContext();
        UserContextParam popMerchantUser = OrderCalcTestDataBuilder.createPopMerchantUserContext();

        // When
        List<TakeActualPriceVO> singleStoreResult = orderCalcService.takePriceHandler(orderVO, singleStoreUser);
        List<TakeActualPriceVO> majorMerchantResult = orderCalcService.takePriceHandler(orderVO, majorMerchantUser);
        List<TakeActualPriceVO> popMerchantResult = orderCalcService.takePriceHandler(orderVO, popMerchantUser);

        // Then
        assertNotNull(singleStoreResult, "单店用户结果不应为空");
        assertNotNull(majorMerchantResult, "大客户结果不应为空");
        assertNotNull(popMerchantResult, "POP用户结果不应为空");

        // 大客户应该返回空结果
        assertTrue(majorMerchantResult.isEmpty(), "大客户应该返回空的到手价列表");
        
        // 单店和POP用户应该有结果（如果SKU存在）
        System.out.println("单店用户结果数量: " + singleStoreResult.size());
        System.out.println("大客户结果数量: " + majorMerchantResult.size());
        System.out.println("POP用户结果数量: " + popMerchantResult.size());
    }

    @Test
    @DisplayName("集成测试 - 省心送到手价计算")
    void testTimingOrderIntegration() {
        // Given
        PlaceOrderVO timingOrderVO = OrderCalcTestDataBuilder.createTimingPlaceOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(timingOrderVO, userContext);

        // Then
        assertNotNull(result, "省心送到手价结果不应为空");
        
        System.out.println("省心送订单处理结果数量: " + result.size());
        
        // 验证省心送特有的逻辑
        if (!result.isEmpty()) {
            for (TakeActualPriceVO priceVO : result) {
                assertNotNull(priceVO.getSku(), "省心送SKU不应为空");
                assertNotNull(priceVO.getTakeActualPrice(), "省心送到手价不应为空");
                System.out.println("省心送SKU: " + priceVO.getSku() + ", 到手价: " + priceVO.getTakeActualPrice());
            }
        }
    }

    @Test
    @DisplayName("集成测试 - 复杂订单场景")
    void testComplexOrderScenario() {
        // Given - 创建包含多种商品的复杂订单
        PlaceOrderVO complexOrderVO = OrderCalcTestDataBuilder.createMixedProductOrderVO();
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When
        List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(complexOrderVO, userContext);

        // Then
        assertNotNull(result, "复杂订单结果不应为空");
        
        System.out.println("复杂订单处理结果:");
        for (TakeActualPriceVO priceVO : result) {
            System.out.println("SKU: " + priceVO.getSku() + 
                             ", 数量: " + priceVO.getQuantity() + 
                             ", 原价: " + priceVO.getPrice() + 
                             ", 到手价: " + priceVO.getTakeActualPrice());
            
            // 基本验证
            assertNotNull(priceVO.getSku(), "SKU不应为空");
            assertNotNull(priceVO.getQuantity(), "数量不应为空");
            assertTrue(priceVO.getQuantity() > 0, "数量应该大于0");
        }
    }

    @Test
    @DisplayName("集成测试 - 数据一致性验证")
    void testDataConsistency() {
        // Given
        String testSku = "863080734120";
        Integer quantity = 3;
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(testSku, quantity);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When - 多次调用同样的参数
        List<TakeActualPriceVO> result1 = orderCalcService.takePriceHandler(orderVO, userContext);
        List<TakeActualPriceVO> result2 = orderCalcService.takePriceHandler(orderVO, userContext);
        List<TakeActualPriceVO> result3 = orderCalcService.takePriceHandler(orderVO, userContext);

        // Then - 验证结果一致性
        assertEquals(result1.size(), result2.size(), "多次调用结果数量应该一致");
        assertEquals(result2.size(), result3.size(), "多次调用结果数量应该一致");

        if (!result1.isEmpty() && !result2.isEmpty() && !result3.isEmpty()) {
            TakeActualPriceVO price1 = result1.get(0);
            TakeActualPriceVO price2 = result2.get(0);
            TakeActualPriceVO price3 = result3.get(0);

            assertEquals(price1.getSku(), price2.getSku(), "SKU应该一致");
            assertEquals(price2.getSku(), price3.getSku(), "SKU应该一致");
            
            assertEquals(price1.getQuantity(), price2.getQuantity(), "数量应该一致");
            assertEquals(price2.getQuantity(), price3.getQuantity(), "数量应该一致");
            
            // 价格可能因为时间相关的优惠而有所不同，这里只做基本检查
            assertNotNull(price1.getTakeActualPrice(), "到手价1不应为空");
            assertNotNull(price2.getTakeActualPrice(), "到手价2不应为空");
            assertNotNull(price3.getTakeActualPrice(), "到手价3不应为空");
        }
    }

    @Test
    @DisplayName("集成测试 - 错误处理能力")
    void testErrorHandling() {
        // Given - 使用不存在的SKU
        String nonExistentSku = "NON_EXISTENT_SKU_999999";
        PlaceOrderVO orderVO = OrderCalcTestDataBuilder.createSpecificSkuOrderVO(nonExistentSku, 1);
        UserContextParam userContext = OrderCalcTestDataBuilder.createSingleStoreUserContext();

        // When & Then - 应该能优雅处理不存在的SKU
        assertDoesNotThrow(() -> {
            List<TakeActualPriceVO> result = orderCalcService.takePriceHandler(orderVO, userContext);
            assertNotNull(result, "即使SKU不存在，也应该返回结果");
            System.out.println("不存在SKU的处理结果数量: " + result.size());
        }, "不存在的SKU应该能优雅处理");
    }
}
