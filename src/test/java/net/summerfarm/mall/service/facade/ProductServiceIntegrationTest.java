package net.summerfarm.mall.service.facade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.mall.MallApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MallApplication.class)
public class ProductServiceIntegrationTest {

    @Resource
    private ProductServiceFacade productServiceIntegration;

    @Test
    public void expireDateTest() {

    }
}