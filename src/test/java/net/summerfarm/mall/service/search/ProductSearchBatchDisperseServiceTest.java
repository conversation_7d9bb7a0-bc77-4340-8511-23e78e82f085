package net.summerfarm.mall.service.search;

import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.service.search.function.SkuCategoryFunction;
import net.summerfarm.mall.service.search.function.SkuSalesVolumeFunction;
import net.summerfarm.mall.service.search.function.SkuStockFunction;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * ProductSearchBatchDisperseService 测试类
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ProductSearchBatchDisperseServiceTest {
    
    @Autowired
    private ProductSearchBatchDisperseService productSearchBatchDisperseService;
    
    @Autowired
    private SkuSalesVolumeCacheService skuSalesVolumeCacheService;
    
    @Test
    public void testBatchDisperseSkusWithStock() {
        // 创建测试数据
        List<EsMarketItemInfoDTO> skuList = createTestSkuList();
        
        // 定义库存检查函数
        SkuStockFunction stockFunction = sku -> sku.getStoreQuantity() != null && sku.getStoreQuantity() > 0;
        
        // 定义销量获取函数
        SkuSalesVolumeFunction salesVolumeFunction = sku -> skuSalesVolumeCacheService.getSkuSalesVolume(sku.getItemCode());
        
        // 定义类目获取函数
        SkuCategoryFunction categoryFunction = EsMarketItemInfoDTO::getCategoryId;
        
        // 执行批量分散重排序
        List<EsMarketItemInfoDTO> result = productSearchBatchDisperseService.batchDisperseSkus(
                skuList, stockFunction, 5, salesVolumeFunction, categoryFunction);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(skuList.size(), result.size());
        
        // 验证有库存的SKU在前面
        boolean foundOutOfStock = false;
        for (EsMarketItemInfoDTO sku : result) {
            boolean hasStock = stockFunction.apply(sku);
            if (!hasStock) {
                foundOutOfStock = true;
            } else if (foundOutOfStock) {
                // 如果已经遇到无库存的SKU，后面不应该再有有库存的SKU（在同一批次内）
                // 这里简化验证逻辑
                break;
            }
        }
    }
    
    @Test
    public void testBatchDisperseSkusWithDefaultStock() {
        List<EsMarketItemInfoDTO> skuList = createTestSkuList();
        
        List<EsMarketItemInfoDTO> result = productSearchBatchDisperseService.batchDisperseSkusWithDefaultStock(
                skuList, 5);
        
        assertNotNull(result);
        assertEquals(skuList.size(), result.size());
    }
    
    @Test
    public void testBatchDisperseSkusBySalesOnly() {
        List<EsMarketItemInfoDTO> skuList = createTestSkuList();
        
        List<EsMarketItemInfoDTO> result = productSearchBatchDisperseService.batchDisperseSkusBySalesOnly(
                skuList, 5);
        
        assertNotNull(result);
        assertEquals(skuList.size(), result.size());
    }
    
    @Test
    public void testBatchDisperseSkusByStockOnly() {
        List<EsMarketItemInfoDTO> skuList = createTestSkuList();
        
        List<EsMarketItemInfoDTO> result = productSearchBatchDisperseService.batchDisperseSkusByStockOnly(
                skuList, 5);
        
        assertNotNull(result);
        assertEquals(skuList.size(), result.size());
    }
    
    @Test
    public void testEmptyList() {
        List<EsMarketItemInfoDTO> emptyList = new ArrayList<>();
        
        List<EsMarketItemInfoDTO> result = productSearchBatchDisperseService.batchDisperseSkusWithDefaultStock(
                emptyList, 5);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    
    @Test
    public void testInvalidBatchSize() {
        List<EsMarketItemInfoDTO> skuList = createTestSkuList();
        
        List<EsMarketItemInfoDTO> result = productSearchBatchDisperseService.batchDisperseSkusWithDefaultStock(
                skuList, 0);
        
        assertNotNull(result);
        assertEquals(skuList.size(), result.size());
    }
    
    /**
     * 创建测试用的SKU列表
     */
    private List<EsMarketItemInfoDTO> createTestSkuList() {
        List<EsMarketItemInfoDTO> skuList = new ArrayList<>();
        
        for (int i = 1; i <= 25; i++) {
            EsMarketItemInfoDTO sku = new EsMarketItemInfoDTO();
            sku.setItemCode("SKU" + String.format("%03d", i));
            sku.setCategoryId((long) (i % 3 + 1)); // 类目1, 2, 3
            sku.setStoreQuantity(i % 4 == 0 ? 0 : i * 10); // 每4个SKU中有1个无库存
            sku.setCoreStoreQuantity(i % 5 == 0 ? 0 : i * 15); // 每5个SKU中有1个无库存
            sku.setMarketItemTitle("商品" + i);
            
            // 注释掉模拟销量数据设置，因为新的服务从Redis获取真实数据
            // skuSalesVolumeCacheService.setSalesVolume(sku.getItemCode(), (long) (i * 100));
            
            skuList.add(sku);
        }
        
        return skuList;
    }
}
