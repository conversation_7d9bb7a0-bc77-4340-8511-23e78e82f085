package net.summerfarm.mall.service.strategy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.recommend.strategy.model.UserExperimentVariantDTO;
import net.summerfarm.recommend.strategy.service.UserExperimentingService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Random;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles("dev2")
public class AbStrategyServiceTest {

    @Resource
    private UserExperimentingService userExperimentingService;


    @Test
    public void testForFakedUser() {
        Random random = new Random();
        List<UserExperimentVariantDTO> userExperimentVariantDTOList = userExperimentingService.getExperimentVariant(123L, 1001);
        log.info("result:{}", userExperimentVariantDTOList);
        Map<String, Integer> variantStatic = Maps.newHashMap();
        for (int idx = 0; idx < 10; idx++) {
            userExperimentVariantDTOList = userExperimentingService.getExperimentVariant(random.nextInt(40_0000) * 1L, 1001);
            log.info("result:{}", userExperimentVariantDTOList);
            Assert.assertFalse(CollectionUtils.isEmpty(userExperimentVariantDTOList));
            userExperimentVariantDTOList.forEach(userExperimentVariantDTO -> {
                final String variantId = userExperimentVariantDTO.getVariantId();
                if (!variantStatic.containsKey(variantId)) {
                    variantStatic.put(variantId, 1);
                } else {
                    variantStatic.put(variantId, 1 + variantStatic.get(variantId));
                }
            });
        }
        log.info("variantStatic:{}", JSON.toJSONString(variantStatic));
        Assert.assertTrue(3 <= variantStatic.size());
    }
}
