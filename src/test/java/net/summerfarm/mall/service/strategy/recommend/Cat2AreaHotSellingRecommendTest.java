package net.summerfarm.mall.service.strategy.recommend;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseMvcTest;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

public class Cat2AreaHotSellingRecommendTest extends BaseMvcTest {

    @Override
    public Long loginMId() {
        return 14541L;
    }

    @Resource
    private Cat2AreaHotSellingRecommend cat2AreaHotSellingRecommend;

    @Test
    void recommendSkus() {
        List<String> recommended = cat2AreaHotSellingRecommend.recommendSkus("5415253385");
        System.out.println(recommended.size());
    }
}