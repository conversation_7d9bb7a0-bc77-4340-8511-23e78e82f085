package net.summerfarm.mall;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.common.util.AesUtil;
import net.summerfarm.mall.mapper.CompanyAccountMapper;
import net.summerfarm.mall.model.domain.CompanyAccount;
import net.summerfarm.mall.model.vo.WxAccountInfoVO;
import net.summerfarm.mall.service.MerchantCouponService;
import net.summerfarm.mall.wechat.utils.XMLUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-05-20
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BaseServiceTest {
    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private MerchantCouponService merchantCouponService;
//    @Resource
//    private MerchantMapper merchantMapper;
//    @Resource
//    private MerchantSubAccountMapper merchantSubAccountMapper;
//    @Resource
//    private AreaMapper areaMapper;
//
//    public void mockLogin(Long mId){
//        Merchant merchant = merchantMapper.selectOneByMid(mId);
//        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), merchant.getOpenid(), null, merchant.getPhone(), 1, merchant.getIslock());
//        merchantSubject.setUnionid(merchant.getUnionid());
//        merchantSubject.setAdminId(merchant.getAdminId());
//        merchantSubject.setDirect(merchant.getDirect());
//        merchantSubject.setSkuShow(merchant.getSkuShow());
//        merchantSubject.setSize(merchant.getSize());
//        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));
//        MerchantSubAccount account = merchantSubAccountMapper.queryByOpenId(merchant.getOpenid());
//        merchantSubject.setAccount(account);
//        merchantSubject.setServer(merchant.getServer());
//        //用户城市信息
//        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
//        merchantSubject.setArea(area);
//
//        RequestHolder.getSession().setAttribute(Global.MERCHANT_SUBJECT, merchantSubject);
//        RequestHolder.getSession().setAttribute(Global.MERCHANT_ID, merchant.getmId());
//        RequestHolder.getSession().setAttribute(Global.ACCOUNT_ID, account.getAccountId());
//        RequestHolder.getSession().setAttribute(Global.OPEN_ID, account.getOpenid());
//    }
//
//    @Resource
//    private AdvanceSaleService advanceSaleService;
//    @Resource
//    private InventoryService inventoryService;
//    @Resource
//    private OrderService orderService;
//    @Resource
//    private WeChatPayService weChatPayService;
//    @Resource
//    private FrontCategoryService frontCategoryService;
//    @Resource
//    private PanicBuyService panicBuyService;
//
//
//    @Test
//    public void preOrder() throws InterruptedException {
//        mockLogin(1340L);
//
//        panicBuyService.panicBuyPreOrder(411, 2, 4275);
//    }
//
//    @Resource
//    private PaymentHandler paymentHandler;
//    @Resource
//    private CmbNotifyService cmbNotifyService;
//    @Resource
//    private AfterSaleOrderService afterSaleOrderService;
//    @Resource
//    private RechargeController rechargeController;

    @Test
    public void xxx() throws Exception {
//        mockLogin(1282L);

        //producer.sendDataToQueue(RocketMqMessageConstant.MANAGE_LIST, 996);
    }

    @Test
    public void xx() throws Exception {
//        mockLogin(1282L);
        Map resultMap = XMLUtil.xmlToMap("<xml><return_code>SUCCESS</return_code><appid><![CDATA[wx0c724490605e1ad6]]></appid><mch_id><![CDATA[1514620471]]></mch_id><nonce_str><![CDATA[7bcdc7b76e992673674b18de34cc2d97]]></nonce_str><req_info><![CDATA[f7S9jv/MZipQl6W8F2YhUbrqYZ5XBObGyA0YhlTL5qHlC7XAf5jRNSkPosM8YpuHTqklepfZ+bwc0x3rlTSHrFLoiie0wTGZnNLvIgBETswSbn5XyNvkXgOYQI8xTe5KzMaKsPiLI4c6VUDX0mnVkwas5eCLIJ3BrLJ09scRWuiR4drOM+nx58TiQTAoBpCgFR1Obp6IbgPTuX1pUHLefBVexYHtJERyitql07WHeJjRSt3ixzqABLTSnkfiA1fekYWGEdzhRP/k5zKuYFm48U81ya9yCBSZRHVxcLBzKB9hjI8eO9Sjq50TeIdaX0dwsk7v/9giiqjKacwL3Y7MAetXBzwlu/NOX6L75jst+dBABnW7HJ45CN6cPFDLvXf9V+9dwDzCmYMo/Rcik/aK6IZtsHRBqZPUa4ws9DIuBz7RqcWVZJQlEjcwEbD6rlcgNLh3p4CpVIIZBQqFtahL3bcz77ocjtt9jZmFXtQB0HflW4coOvhRzvtlqX+I3DH84DKf5kflB4OH7naSvixhIYsBNkuhksxXvkfEB0yUMD75Jxm/pkDPPq4sz7aCytHOmBQgnfekcrf2EYaRETwtdOzAi7fLDths33WSveHSuMOeYDEf9kK4hoJvGmbAmJ9DaghKJDK6Lomlazes9iX3rlXwfQqMRpugclSyLXFOaM31fa26tJ10xyyHPaoPjbj0ewRbJZDjqkB6I9RvwOjR2B/bKT3aSSITbdz/StDPlL35D3JznwlIPIu9hLO2a1aH22pcvnfvvRQID0eqrYd5WJfDS2CcuBCzH1UPmxm1ZwZt0kzwprek/6Fe+QaKBr18itEEvW+lc3Dv7zBiLsjFOhzlFV0SBlJlB7/NiMtmDPu6OWDvRG+4/sU3m4dLNvCDRGu3xCUDCpuwRmvqfGtw1QOUgGauHDrD9SgnyF9rnCdW87/YRDjME7IX6Q73mjewp1ZuJSJ9SJ9A6w7DexWt1h8CTVtlBzwZB1LZDqciiOMF0MWjZ2Uwlwb23XKJaYJzYU2VwJCdYXq9VmCn4bvUo4MjkP16vsGliydebgjCN6gD0B4eYVkGijmWL8cDVFquiYGIukhPGDdzdhkiZeJ16g==]]></req_info></xml>");
//        producer.sendDataToQueue(RocketMqMessageConstant.MANAGE_LIST, 996);
        resultMap.get("req_info");
        resultMap.get("nonce_str");
        resultMap.get("appid");
        resultMap.get("mch_id");
        List<CompanyAccount> companyAccounts = companyAccountMapper.selectByAppId((String) resultMap.get("appid"));
        WxAccountInfoVO wxAccountInfoVO = JSON.parseObject(
                companyAccounts.get(0).getWxAccountInfo(), WxAccountInfoVO.class);
        String sign = wxAccountInfoVO.getWxMchKey();
        log.println(sign);
        String req_info = AesUtil.decryptData((String) resultMap.get("req_info"),sign);
        Map<String, String> stringStringMap = XMLUtil.xmlToMap(req_info);

        System.out.println(req_info);
        log.println(resultMap);
        log.println(stringStringMap);
        resultMap.remove("req_info");
        resultMap.putAll(stringStringMap);
        log.println(resultMap);


    }


    @Test
    public void zx(){
     byte a = (byte) RefundStatusEnum.IN_HANLING.ordinal();
     byte b = (byte) RefundStatusEnum.SUCCESS.ordinal();
     byte c = (byte) RefundStatusEnum.FAIL.ordinal();
     byte d = (byte) RefundStatusEnum.APPLY.ordinal();
     byte e = (byte) RefundStatusEnum.REFUSE.ordinal();

        System.out.println(a+","+b+","+c+","+d+","+e);
    }

}
