package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import org.junit.jupiter.api.Test;

/**
 * @Author: zhuyantao
 * @date: 2023/3/17 5:00 下午
 * @description
 */
public class DeliveryElControlerTest extends BaseControllerTest {

    @Test
    void drawing() throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderNo","02167392425613532");
        jsonObject.put("deliveryPlanId",5610944L);
        jsonObject.put("satisfactionLevel",1);
        jsonObject.put("tag","test,test");
        jsonObject.put("remark","test");
        postTest("/delivery-plan/evaluation/save","");
    }
}
