package net.summerfarm.mall.controller;

import com.alibaba.fastjson.JSON;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Contact;
import net.summerfarm.mall.service.ContactService;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * OrderController.getDefaultContact 接口回归测试
 * 
 * 测试接口: POST /query/default-contact
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OrderController.getDefaultContact 回归测试")
class OrderControllerGetDefaultContactTest {

    @Mock
    private ContactService contactService;

    @InjectMocks
    private OrderController orderController;

    private MockMvc mockMvc;
    private MockedStatic<RequestHolder> mockedRequestHolder;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(orderController).build();
        mockedRequestHolder = mockStatic(RequestHolder.class);
    }

    @AfterEach
    void tearDown() {
        mockedRequestHolder.close();
    }

    // ========== 正常业务场景测试 ==========

    @Test
    @DisplayName("正常场景1: 成功获取默认联系人")
    void testGetDefaultContact_Success() throws Exception {
        // Given: 正常的商户ID和默认联系人
        Long merchantId = 12345L;
        Contact expectedContact = createValidContact(merchantId, 67890L, "张三", "13800138000");
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId)).thenReturn(expectedContact);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        // 验证响应内容
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "响应内容不应为空");
        
        // 验证服务调用
        verify(contactService).getMerchantDefaultContact(merchantId);
        
        // 验证响应结构（假设CommonResult有标准的JSON结构）
        assertTrue(responseContent.contains("\"code\""), "响应应包含code字段");
        assertTrue(responseContent.contains("\"data\""), "响应应包含data字段");
    }

    @Test
    @DisplayName("正常场景2: 大客户获取默认联系人")
    void testGetDefaultContact_MajorCustomer() throws Exception {
        // Given: 大客户场景
        Long majorCustomerId = 88888L;
        Contact majorContact = createMajorCustomerContact(majorCustomerId, 99999L, "李四", "13900139000");
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(majorCustomerId);
        when(contactService.getMerchantDefaultContact(majorCustomerId)).thenReturn(majorContact);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        // 验证大客户联系人信息
        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "大客户响应内容不应为空");
        
        verify(contactService).getMerchantDefaultContact(majorCustomerId);
    }

    @Test
    @DisplayName("正常场景3: 包含完整地址信息的联系人")
    void testGetDefaultContact_CompleteAddressInfo() throws Exception {
        // Given: 包含完整地址信息的联系人
        Long merchantId = 33333L;
        Contact completeContact = createCompleteAddressContact(merchantId, 44444L);
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId)).thenReturn(completeContact);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "完整地址信息响应不应为空");
        
        verify(contactService).getMerchantDefaultContact(merchantId);
    }

    // ========== 边界条件和异常场景测试 ==========

    @Test
    @DisplayName("边界条件1: 商户无默认联系人")
    void testGetDefaultContact_NoDefaultContact() throws Exception {
        // Given: 商户没有默认联系人
        Long merchantId = 55555L;
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId)).thenReturn(null);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "无默认联系人时响应不应为空");
        
        // 验证返回null的情况
        assertTrue(responseContent.contains("null") || responseContent.contains("\"data\":null"), 
                "无默认联系人时data应为null");
        
        verify(contactService).getMerchantDefaultContact(merchantId);
    }

    @Test
    @DisplayName("边界条件2: RequestHolder返回null商户ID")
    void testGetDefaultContact_NullMerchantId() throws Exception {
        // Given: RequestHolder返回null
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(null);
        when(contactService.getMerchantDefaultContact(null)).thenReturn(null);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "null商户ID时响应不应为空");
        
        verify(contactService).getMerchantDefaultContact(null);
    }

    @Test
    @DisplayName("异常场景1: ContactService抛出异常")
    void testGetDefaultContact_ServiceException() throws Exception {
        // Given: ContactService抛出异常
        Long merchantId = 77777L;
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then: 验证异常处理
        assertThrows(RuntimeException.class, () -> {
            mockMvc.perform(post("/query/default-contact")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        });

        verify(contactService).getMerchantDefaultContact(merchantId);
    }

    // ========== 数据完整性测试 ==========

    @Test
    @DisplayName("数据完整性1: 联系人字段完整性验证")
    void testGetDefaultContact_ContactFieldIntegrity() throws Exception {
        // Given: 包含所有关键字段的联系人
        Long merchantId = 11111L;
        Contact contact = new Contact();
        contact.setmId(merchantId);
        contact.setContactId(22222L);
        contact.setContact("王五");
        contact.setPhone("13700137000");
        contact.setProvince("浙江省");
        contact.setCity("杭州市");
        contact.setArea("西湖区");
        contact.setAddress("文三路123号");
        contact.setIsDefault(1);
        contact.setStoreNo(101);
        contact.setDeliveryFee(BigDecimal.valueOf(5.00));
        contact.setLastDeliveryTime(LocalTime.of(18, 30));
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId)).thenReturn(contact);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "字段完整性测试响应不应为空");
        
        // 验证关键字段存在（基于JSON响应）
        assertTrue(responseContent.contains("王五"), "响应应包含联系人姓名");
        assertTrue(responseContent.contains("13700137000"), "响应应包含电话号码");
        assertTrue(responseContent.contains("杭州市"), "响应应包含城市信息");
        
        verify(contactService).getMerchantDefaultContact(merchantId);
    }

    @Test
    @DisplayName("数据完整性2: 特殊字符处理")
    void testGetDefaultContact_SpecialCharacters() throws Exception {
        // Given: 包含特殊字符的联系人信息
        Long merchantId = 99999L;
        Contact contact = createValidContact(merchantId, 11111L, "张三&李四", "13800138000");
        contact.setAddress("测试地址<script>alert('test')</script>");
        contact.setRemark("备注信息\"引号\"测试");
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId)).thenReturn(contact);

        // When & Then
        MvcResult result = mockMvc.perform(post("/query/default-contact")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        assertNotNull(responseContent, "特殊字符处理响应不应为空");
        
        // 验证特殊字符被正确处理
        assertFalse(responseContent.contains("<script>"), "响应不应包含未转义的脚本标签");
        
        verify(contactService).getMerchantDefaultContact(merchantId);
    }

    // ========== 性能和并发测试 ==========

    @Test
    @DisplayName("性能测试: 多次调用一致性")
    void testGetDefaultContact_MultipleCallsConsistency() throws Exception {
        // Given: 相同的商户ID多次调用
        Long merchantId = 12121L;
        Contact contact = createValidContact(merchantId, 21212L, "赵六", "13600136000");
        
        mockedRequestHolder.when(RequestHolder::getMId).thenReturn(merchantId);
        when(contactService.getMerchantDefaultContact(merchantId)).thenReturn(contact);

        // When: 多次调用接口
        for (int i = 0; i < 3; i++) {
            MvcResult result = mockMvc.perform(post("/query/default-contact")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andReturn();

            String responseContent = result.getResponse().getContentAsString();
            assertNotNull(responseContent, "第" + (i + 1) + "次调用响应不应为空");
            assertTrue(responseContent.contains("赵六"), "每次调用应返回相同的联系人");
        }

        // Then: 验证服务被调用了3次
        verify(contactService, times(3)).getMerchantDefaultContact(merchantId);
    }

    // ========== 辅助方法 ==========

    /**
     * 创建有效的联系人对象
     */
    private Contact createValidContact(Long mId, Long contactId, String name, String phone) {
        Contact contact = new Contact();
        contact.setmId(mId);
        contact.setContactId(contactId);
        contact.setContact(name);
        contact.setPhone(phone);
        contact.setProvince("浙江省");
        contact.setCity("杭州市");
        contact.setArea("余杭区");
        contact.setAddress("测试地址123号");
        contact.setIsDefault(1);
        contact.setStoreNo(101);
        return contact;
    }

    /**
     * 创建大客户联系人对象
     */
    private Contact createMajorCustomerContact(Long mId, Long contactId, String name, String phone) {
        Contact contact = createValidContact(mId, contactId, name, phone);
        contact.setCity("深圳市");
        contact.setArea("福田区");
        contact.setAddress("深南大道1000号");
        contact.setStoreNo(2001);
        contact.setDeliveryFee(BigDecimal.valueOf(0.00)); // 大客户免运费
        return contact;
    }

    /**
     * 创建包含完整地址信息的联系人对象
     */
    private Contact createCompleteAddressContact(Long mId, Long contactId) {
        Contact contact = createValidContact(mId, contactId, "完整信息用户", "13500135000");
        contact.setEmail("<EMAIL>");
        contact.setPosition("采购经理");
        contact.setGender(true);
        contact.setWeixincode("wx123456");
        contact.setHouseNumber("A座1001室");
        contact.setPoiNote("地标建筑旁边");
        contact.setRemark("重要客户，优先配送");
        contact.setDeliveryFee(BigDecimal.valueOf(8.00));
        contact.setLastDeliveryTime(LocalTime.of(19, 0));
        contact.setSupportAddOrder(0); // 支持加单
        return contact;
    }
}
