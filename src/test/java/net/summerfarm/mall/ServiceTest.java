package net.summerfarm.mall;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.CloseSaleEnum;
import net.summerfarm.enums.OpenSaleEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.util.MailUtil;
import net.summerfarm.mall.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.factory.AfterSaleCalculatorFactory;
import net.summerfarm.mall.mapper.AreaSkuMapper;
import net.summerfarm.mall.mapper.DistributionRuleMapper;
import net.summerfarm.mall.mapper.TimingRuleMapper;
import net.summerfarm.mall.model.Email;
import net.summerfarm.mall.model.domain.AreaSku;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.TimingRule;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.CartTimingVO;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.wechat.enums.AfterSaleCalculatorType;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Package:
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/11/21
 */


public class ServiceTest extends BaseServiceTest {

    @Resource
    private OrderService orderService;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Autowired
    private RedisTemplate<Serializable, Object> template;

    @Resource
    private MerchantCouponService merchantCouponService;

    @Resource
    private DistributionRuleMapper distributionRuleMapper;

    @Resource
    private AreaSkuService areaSkuService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private AfterSaleCalculatorFactory afterSaleCalculatorFactory;

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Autowired
    private MailUtil mailUtil;
    //会红线 但是必须指定
//    @Resource(name="redisTemplate")
//    private ValueOperations<String, String> valueOps;

    @Test
    public void redisDemo() {
        System.out.println(template.hasKey("s"));
        System.out.println(template.hasKey("q"));
        template.opsForValue().set("t", "good");
        System.out.println(template.opsForValue().get("s"));
        System.out.println(template.opsForValue().get("t"));
    }

    @Test
    public void mailDemo() {
//        mailUtil.sendMail("11","22");
        String data = "{\"data\":{\"cc\":[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\"subject\":\"仓库库存售罄\",\"text\":\"李星星，你好，你采购的E001S04R004仓库库存为0，请注意及时采购。\",\"to\":[\"<EMAIL>\"]},\"type\":\"EMAIL\"}";
        MQData mqData = JSON.toJavaObject(JSON.parseObject(data), MQData.class);
        Email email = JSON.parseObject(mqData.getData().toString(), Email.class);
        mailUtil.sendMail(email.getSubject(), email.getText(), email.getTo(), email.getCc());
    }


    @Test
    public void testTiming() {
        Date now = new Date();
        TimingRule timingRule = timingRuleMapper.selectByPrimaryKey(5);
        if (now.before(timingRule.getStartTime()) || now.after(timingRule.getEndTime())) {
            System.out.println(ResultConstant.TIMING_OUT_OF_TIME);
        }

    }

    @Test
    public void countUnused() {
        AjaxResult aj = merchantCouponService.countUnused(1L);
//        System.out.println( "#########################33"+merchantService.checkPhone("17326096760"));
        System.out.println(JSON.toJSONString(aj));
    }


    @Test
    public void palceOrder() {
        MerchantSubject ms = new MerchantSubject();
        ms.setMerchantId(25L);

        List<CartTimingVO> cartTimingVOs = new ArrayList<>();
        CartTimingVO cartTimingVO = new CartTimingVO();
        cartTimingVO.setDeliveryQuantity(3);
        cartTimingVO.setTimingOrderNo("02149214095547395");
        cartTimingVOs.add(cartTimingVO);
        //{"mcontact":"西索","mphone":"18767121811","coupon":"","cartTimingVOs":[{"deliveryQuantity":3,"timingOrderNo":"02149214095547395"}]}
    }

    @Test
    public void getOrdersTest() {
//        // orderService.updateStock("2274182135", 2750, 2, null, null, null, null, true, 438L);
    }

    @Test
    public void selectByCategoryId() {
        // // orderService.updateStock("5401607086",1001,2, "123",SaleStockChangeTypeEnum.PLACE_ORDER,"23",null,false);
    }

    @Test
    public void cbd() {
        Merchant merchant = new Merchant();
        merchant.setmId(1228L);
        merchant.setMcontact("124214");
        merchant.setProvince("浙江");
        merchant.setCity("杭州市");
        merchant.setArea("拱墅区");
        merchant.setAddress("124234234");
        merchant.setPoiNote("1241234124,24235234");
        merchantService.registerToCBD(merchant);
    }

    @Test
    public void after(){
        AfterSaleOrderVO afterSaleOrder = new AfterSaleOrderVO();
        afterSaleOrder.setOrderNo("01163633948386418");
        afterSaleOrder.setQuantity(2);
        afterSaleOrder.setSku("863080734120");
        afterSaleOrder.setSuitId(0);
        AfterSaleCalculator calculator = this.afterSaleCalculatorFactory.getAfterSaleCalculator(AfterSaleCalculatorType.NOT_NEED.getType());
        System.out.println(calculator.getClass());
        AjaxResult ajaxResult = calculator.calcRefund(afterSaleOrder);
        System.out.println(ajaxResult.getData());
    }

    @Test
    public void testOpenSaleForerver() {
        String sku = "4831436200";
        int areaNo = 1001;

        // 初始化数据
        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        areaSku.setOnSale(false);
        areaSku.setOpenSale(OpenSaleEnum.STOCK_TURN_ON_FOREVER.ordinal());
        areaSku.setCloseSale(null);
        areaSkuMapper.updateByPrimaryKey(areaSku);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertFalse(areaSku.getOnSale());

        // 添加库存到大于0
        // // orderService.updateStock(sku, 1, 2, null, SaleStockChangeTypeEnum.CANCEL_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertTrue(areaSku.getOnSale());

        //减少库存到0
        // // orderService.updateStock(sku, 1, -2, null, SaleStockChangeTypeEnum.PLACE_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertFalse(areaSku.getOnSale());

        // 重新添加库存到大于0
        // orderService.updateStock(sku, 1, 2, null, SaleStockChangeTypeEnum.CANCEL_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertTrue(areaSku.getOnSale());

        // 重新减少库存到0
        // orderService.updateStock(sku, 1, -2, null, SaleStockChangeTypeEnum.PLACE_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertFalse(areaSku.getOnSale());
    }

    @Test
    public void testOpenSale() {
        String sku = "4831436200";
        int areaNo = 1001;

        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        areaSku.setOnSale(false);
        areaSku.setOpenSale(OpenSaleEnum.STOCK_TURN_ON.ordinal());
        areaSku.setCloseSale(CloseSaleEnum.SALE_OUT_TURN_OFF.ordinal());
        areaSkuMapper.updateByPrimaryKey(areaSku);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertFalse(areaSku.getOnSale());

        // 添加库存到大于0
        // orderService.updateStock(sku, 1, 2, null, SaleStockChangeTypeEnum.CANCEL_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertTrue(areaSku.getOnSale());

        //减少库存到0
        // orderService.updateStock(sku, 1, -2, null, SaleStockChangeTypeEnum.PLACE_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertFalse(areaSku.getOnSale());

        // 重新添加库存到大于0
        // orderService.updateStock(sku, 1, 2, null, SaleStockChangeTypeEnum.CANCEL_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertFalse(areaSku.getOnSale());

        //重新添加库存不会上架，重新初始化为在售
        areaSku.setOnSale(true);
        areaSkuMapper.updateOnSale(areaSku);

        // 重新减少库存到0
        // orderService.updateStock(sku, 1, -2, null, SaleStockChangeTypeEnum.PLACE_ORDER, "123", new HashMap<>(), true, 442L);
        areaSku = areaSkuMapper.selectByAreaNoAndSku(areaNo, sku);
        Assert.assertTrue(areaSku.getOnSale());
    }

}
