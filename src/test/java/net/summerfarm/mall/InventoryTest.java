package net.summerfarm.mall;

import com.alibaba.fastjson.JSON;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.model.vo.EsProductVO;
import net.summerfarm.mall.repository.CycleInventoryCostRepository;
import net.summerfarm.mall.service.InventoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-12-11
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class InventoryTest {
    @Resource
    private InventoryService inventoryService;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;

    @Test
    public void testCheckIsFruit(){
        Boolean aBoolean = inventoryMapper.checkIsFruit("5483773214");
        System.out.println(aBoolean);
    }


    @Test
    public void testPriceCost(){
        BigDecimal result = cycleInventoryCostRepository.selectCycleCost("596406424645", 10);
        System.out.println(result);
    }

}
