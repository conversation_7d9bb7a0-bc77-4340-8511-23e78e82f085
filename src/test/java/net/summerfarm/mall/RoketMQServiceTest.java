package net.summerfarm.mall;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.mall.common.delayqueue.AfterSaleProofItem;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.delayqueue.VirtualOrderCancelItem;
import net.summerfarm.mall.common.mq.DelayData;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQDelayConstant;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.mapper.CompanyAccountMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.domain.Payment;
import net.summerfarm.mall.payments.common.delayqueue.PaymentDelayQueueItem;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2021年12月03日 18:22:00
 */
public class RoketMQServiceTest extends BaseServiceTest{

    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private MqProducer mqProducer;

    /**
     * 支付回调
     */
    @Test
    public void notifySuccessTest(){
        String orderNo = "*****************";
        Orders order = ordersMapper.selectByOrderNo(orderNo);
        Payment payment = new Payment();
        payment.setPayType(Global.BOC_PAY);
        payment.setOrderNo(orderNo);
        payment.setTransactionNumber(orderNo);
        BigDecimal money = order.getTotalPrice();
        payment.setMoney(money);
        payment.setEndTime(new Date());
        payment.setStatus(1);
        payment.setBocPayType("WEIX");
        //根据boc mid去查询账号
        payment.setCompanyAccountId(1151);

        PayNotifySuccessData payNotifySuccessData=new PayNotifySuccessData();
        payNotifySuccessData.setPayment(payment);
        payNotifySuccessData.setOrder(order);
        MQData mqData = new MQData();
        mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData), order.getOrderNo());
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), order.getOrderNo());
    }

    /**
     * 关闭超时售后单
     */
    @Test
    public void afterSaleTimeoutCloseTest(){
        String afterSaleOrderNo = "************";
        LocalDateTime updatetime = LocalDateTime.now();
        AfterSaleProofItem afterSaleProofItem = new AfterSaleProofItem("afterSaleOrderNo" + afterSaleOrderNo, updatetime, 24 * 60 * 60 * 1000L, afterSaleOrderNo);
        DelayData delayData = new DelayData();
        delayData.setType(MType.AFTER_SALE_TIMEOUT_CLOSE.name());
        delayData.setData(JSONObject.toJSONString(afterSaleProofItem));
        delayData.setDelayHours(4);
        //producer.sendDelayDataToQueue(MQTopicConstant.MALL_DELAY_LIST, JSONObject.toJSONString(delayData), MQDelayConstant.TWO_DELAY_LEVEL);
    }

    /**
     * 支付反查
     */
    @Test
    public void paymentStatusQueryTest(){
        String payOrderNo = "*****************";
        PaymentDelayQueueItem delayQueueItem = new PaymentDelayQueueItem(payOrderNo, 10 * 1000L);
        MQData payData = new MQData();
        payData.setType(MType.PAYMENT_STATUS_QUERY.name());
        payData.setData(JSONObject.toJSONString(delayQueueItem));
        //producer.sendDelayDataToQueue(MQTopicConstant.MALL_PAYMENT_DELAY_LIST, JSONObject.toJSONString(payData), MQDelayConstant.TWO_DELAY_LEVEL);
        mqProducer.sendDelay(MQTopicConstant.MALL_PAYMENT_DELAY_LIST,null, JSONObject.toJSONString(payData), MQDelayConstant.TWO_DELAY_LEVEL_LONG);
    }

    /**
     * 取消订单
     */
    @Test
    public void orderTimeoutCloseTest(){
        String orderNo = "01163870982535795";
        OrderCancelItem orderCancelItem = new OrderCancelItem("cancelorder" + orderNo, LocalDateTime.now(),
                30 * 60 * 1000L, 1313L, "oBTbNwd5S5nsQN7bZz5MKzbAhVSk", 1001, orderNo);
        DelayData delayData = new DelayData();
        delayData.setType(MType.ORDER_TIMEOUT_CLOSE.name());
        delayData.setData(JSONObject.toJSONString(orderCancelItem));
        //producer.sendDelayDataToQueue(MQTopicConstant.MALL_DELAY_LIST, JSONObject.toJSONString(delayData), MQDelayConstant.TWO_DELAY_LEVEL);
    }

    /**
     * 虚拟商品取消订单
     */
    @Test
    public void virtualOrderTimeoutCloseTest(){
        String orderNo = "01163870982535795";
        //订单延迟取消
        VirtualOrderCancelItem item = new VirtualOrderCancelItem("cancel_virtual_order" + orderNo, LocalDateTime.now(), 30 * 60 * 1000L, orderNo);
        DelayData delayData = new DelayData();
        delayData.setType(MType.VIRTUAL_ORDER_TIMEOUT_CLOSE.name());
        delayData.setData(JSONObject.toJSONString(item));
        //producer.sendDelayDataToQueue(MQTopicConstant.MALL_DELAY_LIST, JSONObject.toJSONString(delayData), MQDelayConstant.TWO_DELAY_LEVEL);
    }
}
