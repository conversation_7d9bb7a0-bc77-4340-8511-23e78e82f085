package net.summerfarm.mall;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.mapper.SkuDiscountMapper;
import net.summerfarm.mall.model.domain.SkuDiscount;
import net.summerfarm.mall.service.CollocationService;
import net.summerfarm.mall.service.TrolleyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CollocationTest {

    @Resource
    private SkuDiscountMapper skuDiscountMapper;

    @Test
    public void ss(){
        List<SkuDiscount> skuDiscounts = skuDiscountMapper.selectBySkus(Arrays.asList("5415253072", "5415253071"));
        System.out.println(skuDiscounts);
    }



}
