package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * @Classname merchantControllerTest
 * @Description TODO
 * @Date 2021/12/6 18:24
 * @Created by admin
 */
public class merchantControllerTest extends BaseControllerTest {

    @Test
    public void orderSelectTest() throws Exception{
        mvc.perform(MockMvcRequestBuilders.post("/merchant/getFreeDay")
                .accept(MediaType.APPLICATION_JSON).contentType(APPLICATION_JSON_UTF8)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


}
