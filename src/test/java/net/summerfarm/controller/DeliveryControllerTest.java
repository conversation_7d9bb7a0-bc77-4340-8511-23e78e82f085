package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class DeliveryControllerTest extends BaseControllerTest {

    @Test
    public void selectDelivertRuleTest() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/delivery")
                .accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
