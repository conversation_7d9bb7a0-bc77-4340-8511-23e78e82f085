package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class RechargeControllerTest extends BaseControllerTest{

    @Test
    public void selectRechargeAmount() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/recharge/amount").accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectRechargeRecordList() throws Exception{
        mvc.perform(MockMvcRequestBuilders.get("/recharge/recharge/record/1/10").accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void getOrders() throws Exception{
        mvc.perform(MockMvcRequestBuilders.get("/order/1/6")
                .accept(MediaType.APPLICATION_JSON)
                .param("type","0")
                .param("orderStatus","3")
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
