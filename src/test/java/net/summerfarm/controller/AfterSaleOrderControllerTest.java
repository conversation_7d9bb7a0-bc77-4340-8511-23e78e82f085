package net.summerfarm.controller;

import com.alibaba.fastjson.JSON;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mall.client.req.afterSale.AfterSaleOrderReq;
import net.summerfarm.mall.client.req.afterSale.ExchangeGoodsReq;
import net.summerfarm.mall.client.req.order.PlaceOrderReq;
import net.summerfarm.mall.client.req.order.TrolleyReq;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.provider.impl.AfterSaleOrderProviderImpl;
import net.summerfarm.mall.provider.impl.OrderProviderImpl;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import org.junit.Test;
import org.springframework.test.web.servlet.MvcResult;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class AfterSaleOrderControllerTest extends BaseControllerTest {

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Resource
    private AfterSaleOrderHelper afterSaleOrderHelper;

    @Resource
    private AfterSaleOrderProviderImpl afterSaleOrderProvider;

    @Resource
    private OrderProviderImpl orderProvider;

    @Test
    public void findPage() throws Exception {

        MvcResult ajaxResult = getTest("/after-sale/1/15");
        System.out.println(ajaxResult);

    }

    @Test
    public void findAfterSaleOrder() throws Exception {

        getTest("/after-sale/order?orderNo=01154537425248612&sku=604782150&suitId=0");

    }

    @Test
    public void pre() throws Exception {

        getTest("/after-sale/pre?orderNo=01154537425248612&sku=604782150");

    }

    @Test
    public void save() throws Exception {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setAfterSaleType("22121");
        afterSaleOrderVO.setAfterSaleUnit("1212");
        afterSaleOrderVO.setApplyRemark("不要返券");
        afterSaleOrderVO.setHandleType(0);
        afterSaleOrderVO.setOrderNo("01154570790018962");
        afterSaleOrderVO.setProofPic("a15460519890810");
        afterSaleOrderVO.setQuantity(1);
        afterSaleOrderVO.setSku("604782150");
        postTest("/after-sale", JSON.toJSONString(afterSaleOrderVO));


    }

    @Test
    public void calcAfterSaleCoupon() {
    }

    @Test
    public void cancel() {
    }

    @Test
    public void updatePicProof() {
    }

    @Test
    public void refund() {
    }

    @Test
    public void handleTest() {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setAfterSaleOrderNo("8440509961760960512");
        afterSaleOrderVO.setAfterSaleQuantity(0);
        afterSaleOrderVO.setAfterSaleTime(0);
        afterSaleOrderVO.setExtraRemark("就是发");
        afterSaleOrderVO.setHandleNum(BigDecimal.valueOf(50.18));
        afterSaleOrderVO.setHandleSecondaryRemark("质量,异物/有虫");
        afterSaleOrderVO.setHandleType(11);
        afterSaleOrderVO.setHandler("zxf");
        afterSaleOrderVO.setOrderNo("01170651320827814");
        afterSaleOrderVO.setQuantity(10);
        afterSaleOrderVO.setRecoveryNum(BigDecimal.valueOf(30));
        afterSaleOrderVO.setRecoveryType(0);
        afterSaleOrderVO.setRefundType("客户原因");
        afterSaleOrderService.newHandle(afterSaleOrderVO);
    }

    @Test
    public void getCouponMoneyforAfterSale() {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setOrderNo("01172604381807421");
        afterSaleOrderVO.setIsManage(Boolean.TRUE);
        afterSaleOrderVO.setSku("2154357323260");
        afterSaleOrderVO.setDeliveryed(1);
        afterSaleOrderVO.setHandleType(2);
        afterSaleOrderVO.setQuantity(40000);
        afterSaleOrderVO.setProductType(0);
        afterSaleOrderVO.setSuitId(0);
        afterSaleOrderHelper.getCouponMoneyforAfterSale(afterSaleOrderVO);
    }

    @Test
    public void afterSaleManageCalculateQuantity() {
        AfterSaleOrderReq afterSaleOrderVO = new AfterSaleOrderReq();
        afterSaleOrderVO.setOrderNo("0124LKQ1SN1209112765");
        afterSaleOrderVO.setIsManage(Boolean.TRUE);
        afterSaleOrderVO.setSku("858641441106");
        afterSaleOrderVO.setDeliveryed(1);
        afterSaleOrderVO.setHandleType(4);
        afterSaleOrderVO.setProductType(0);
        afterSaleOrderVO.setSuitId(0);
        List<ExchangeGoodsReq> exchangeGoodList = new ArrayList<>();
        ExchangeGoodsReq good = new ExchangeGoodsReq();
        good.setQuantity(2);
        good.setSku("296767034742");
        good.setPdName("SaaS报价商品1659340544023");
        good.setWeight("1/猪/一级");
        exchangeGoodList.add(good);
        //afterSaleOrderVO.setExchangeGoodList(exchangeGoodList);
        afterSaleOrderProvider.afterSaleManageCalculateQuantity(afterSaleOrderVO);
    }

    @Test
    public void afterSaleManageAfterSaleMoney() {
        AfterSaleOrderReq afterSaleOrderVO = new AfterSaleOrderReq();
        afterSaleOrderVO.setOrderNo("01172854644365966");
        afterSaleOrderVO.setIsManage(Boolean.TRUE);
        afterSaleOrderVO.setSku("785018631014");
        afterSaleOrderVO.setDeliveryed(1);
        afterSaleOrderVO.setHandleType(5);
        afterSaleOrderVO.setQuantity(2);
        afterSaleOrderVO.setProductType(0);
        afterSaleOrderVO.setSuitId(0);
        List<ExchangeGoodsReq> exchangeGoodList = new ArrayList<>();
        ExchangeGoodsReq good = new ExchangeGoodsReq();
        good.setQuantity(2);
        good.setSku("************");
        good.setPdName("验收代销");
        good.setWeight("打发士大夫/热认为/猪");
        exchangeGoodList.add(good);
        afterSaleOrderVO.setExchangeGoodList(exchangeGoodList);
        afterSaleOrderProvider.afterSaleManageAfterSaleMoney(afterSaleOrderVO);
    }

    @Test
    public void afterSaleManageSave() {
        AfterSaleOrderReq afterSaleOrderVO = new AfterSaleOrderReq();
        afterSaleOrderVO.setOrderNo("0124IXWWYV1101183917");
        afterSaleOrderVO.setIsManage(Boolean.TRUE);
        afterSaleOrderVO.setSku("************");
        afterSaleOrderVO.setDeliveryed(1);
        afterSaleOrderVO.setHandleType(0);
        afterSaleOrderVO.setQuantity(1);
        afterSaleOrderVO.setProductType(0);
        afterSaleOrderVO.setSuitId(0);
        afterSaleOrderVO.setMId(344065L);
        afterSaleOrderVO.setAccountId(3383L);
        afterSaleOrderVO.setHandleNum(BigDecimal.valueOf(2.28));
        afterSaleOrderVO.setAfterSaleType("商品数量不符");
        afterSaleOrderVO.setAfterSaleUnit("111");
        //afterSaleOrderVO.setRecoveryType(1);
        //afterSaleOrderVO.setRecoveryNum(BigDecimal.valueOf(10));
        afterSaleOrderVO.setApplySecondaryRemark("质量,异物/有虫");
        List<ExchangeGoodsReq> exchangeGoodList = new ArrayList<>();
        ExchangeGoodsReq good = new ExchangeGoodsReq();
        good.setQuantity(2);
        good.setSku("************");
        good.setPdName("验收代销");
        good.setWeight("打发士大夫/热认为/猪");
        exchangeGoodList.add(good);
        //afterSaleOrderVO.setExchangeGoodList(exchangeGoodList);
        afterSaleOrderProvider.afterSaleManageSave(afterSaleOrderVO);
    }

    @Test
    public void afterSaleHandle() {
        String str = "{\n" +
                "    \"afterSaleOrderNo\": \"8605796225441136640\",\n" +
                "    \"afterSaleQuantity\": 0,\n" +
                "    \"afterSaleTime\": 0,\n" +
                "    \"auditeRemark\": \"**********\",\n" +
                "    \"auditer\": \"张弛\",\n" +
                "    \"categoryId\": 0,\n" +
                "    \"needPagination\": true,\n" +
                "    \"pageIndex\": 1,\n" +
                "    \"pageSize\": 20,\n" +
                "    \"startRow\": 0,\n" +
                "    \"status\": 1\n" +
                "}";
        AfterSaleOrderReq afterSaleOrderVO = JSON.parseObject(str, AfterSaleOrderReq.class);
        afterSaleOrderProvider.afterSaleAudit(afterSaleOrderVO);
    }

    @Test
    public void orderPlaceOrderV3() {
        PlaceOrderReq placeOrderReq = new PlaceOrderReq();
        placeOrderReq.setContactId(349758L);
        placeOrderReq.setAccountId(9813L);
        placeOrderReq.setMId(350459L);
        placeOrderReq.setHelpOrder(1);
        placeOrderReq.setOrderNo("*****************");
        placeOrderReq.setPayType(3);
        placeOrderReq.setRemark("账期客户下单");
        List<TrolleyReq> orderNow = new ArrayList<>();
        TrolleyReq trolleyReq = new TrolleyReq();
        trolleyReq.setSku("*************");
        trolleyReq.setQuantity(121);
        trolleyReq.setProductType(0);
        trolleyReq.setParentSku("0");
        orderNow.add(trolleyReq);
        placeOrderReq.setOrderNow(orderNow);
        orderProvider.orderPlaceOrderV3(placeOrderReq);
    }
}