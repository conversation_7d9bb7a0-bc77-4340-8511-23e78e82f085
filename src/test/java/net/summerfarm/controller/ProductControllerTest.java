package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.mall.model.vo.PlaceTimingOrderVO;
import net.summerfarm.mall.model.vo.ProductInfoVO;
import net.summerfarm.mall.service.InventoryService;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.annotation.Resource;

public class ProductControllerTest extends BaseControllerTest {

    @Resource
    private InventoryService inventoryService;


    @Test
    public void productInfoSelectTest() throws Exception{
        mvc.perform(MockMvcRequestBuilders.get("/product/1/10").accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void timingProductSelectTest() throws Exception{
        mvc.perform(MockMvcRequestBuilders.get("/timing-delivery/sku/2248674185")
                .param("ruleId","2")
                .accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void orderSelectTest() throws Exception{
        PlaceTimingOrderVO placeTimingOrderVO = new PlaceTimingOrderVO();
        placeTimingOrderVO.setDeliveryTimes(2);
        placeTimingOrderVO.setMcontact("xinxxinx信息");
        placeTimingOrderVO.setMphone("17611451011");
        placeTimingOrderVO.setQuantity(4);
        placeTimingOrderVO.setTimingRuleId(10);
        mvc.perform(MockMvcRequestBuilders.post("/order/timing")
                .accept(MediaType.APPLICATION_JSON).contentType(APPLICATION_JSON_UTF8)
                .content(JSONObject.toJSONString(placeTimingOrderVO))
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void getProductListTest() {
        PageInfo<ProductInfoVO> productPageInfo = inventoryService.getHelpOrderProductList(1, 10,
                349463L, "用户中心报价", 1001, 0, false, 0, 1);
    }
}
