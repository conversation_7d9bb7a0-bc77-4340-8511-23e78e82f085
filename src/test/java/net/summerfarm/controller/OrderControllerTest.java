package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.mall.common.filter.ReplacePriceFilter;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.DeliveryPlan;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.MerchantSubAccountService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Create 2020-10-16
 */
public class OrderControllerTest extends BaseControllerTest {

    @Autowired
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private AreaMapper areaMapper;


    @Test
    public void testDeliveryFee() throws Exception {

        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).addFilters(new ReplacePriceFilter()).build();

        Merchant merchant = merchantMapper.selectOneByMid(84L);

        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), merchant.getOpenid(),
                merchant.getMpOpenid(), merchant.getPhone(), 1, merchant.getIslock());

        MerchantSubAccount account = subAccountService.selectOne("phone", "***********");

        merchantSubject.setUnionid(merchant.getUnionid());
        merchantSubject.setAdminId(merchant.getAdminId());
        merchantSubject.setDirect(merchant.getDirect());
        merchantSubject.setSkuShow(merchant.getSkuShow());
        merchantSubject.setSize(merchant.getSize());
        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));

        merchantSubject.setAccount(account);
        merchantSubject.setServer(merchant.getServer());

        //用户城市信息
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        merchantSubject.setArea(area);

        //客户类型
        Integer merchantType = 2;
        merchantSubject.setMerchantType(merchantType);

        mockHttpSession = new MockHttpSession();
        mockHttpSession.setAttribute(Global.MERCHANT_ID, 84L);
        mockHttpSession.setAttribute(Global.MERCHANT_SUBJECT,merchantSubject);
        mockHttpSession.setAttribute(Global.OPEN_ID,account.getOpenid());
        mockHttpSession.setAttribute(Global.ACCOUNT_ID,account.getAccountId());
        mockHttpSession.setAttribute("phone","***********");
        mockHttpSession.setAttribute("code","123456");

        mvc.perform(MockMvcRequestBuilders.get("/order/delivery-fee")
                .param("fruitPrice","45")
                .param("totalPrice","45")
                .param("categoryList","45")
                .param("cartTiming","false")
                .accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }


    @Test
    public void testPlaceOrder() throws Exception {
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).addFilters(new ReplacePriceFilter()).build();

        Merchant merchant = merchantMapper.selectOneByMid(84L);

        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), merchant.getOpenid(),
                merchant.getMpOpenid(), merchant.getPhone(), 1, merchant.getIslock());

        MerchantSubAccount account = subAccountService.selectOne("phone", "***********");

        merchantSubject.setUnionid(merchant.getUnionid());
        merchantSubject.setAdminId(merchant.getAdminId());
        merchantSubject.setDirect(merchant.getDirect());
        merchantSubject.setSkuShow(merchant.getSkuShow());
        merchantSubject.setSize(merchant.getSize());
        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));

        merchantSubject.setAccount(account);
        merchantSubject.setServer(merchant.getServer());

        //用户城市信息
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        merchantSubject.setArea(area);

        //客户类型
        Integer merchantType = 2;
        merchantSubject.setMerchantType(merchantType);

        mockHttpSession = new MockHttpSession();
        mockHttpSession.setAttribute(Global.MERCHANT_ID, 84L);
        mockHttpSession.setAttribute(Global.MERCHANT_SUBJECT,merchantSubject);
        mockHttpSession.setAttribute(Global.OPEN_ID,account.getOpenid());
        mockHttpSession.setAttribute(Global.ACCOUNT_ID,account.getAccountId());
        mockHttpSession.setAttribute("phone","***********");
        mockHttpSession.setAttribute("code","123456");


        JSONObject param = new JSONObject();
        param.put("contactId", 98);
        param.put("cartTimingVOs", new ArrayList<>());
        param.put("mphone", "***********");
        String content = param.toString();

        mvc.perform(
            MockMvcRequestBuilders.post("/order")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content)
                .accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession)
        ).andExpect(MockMvcResultMatchers.status().isOk())
         .andExpect(MockMvcResultMatchers.forwardedUrl(null))
         .andDo(MockMvcResultHandlers.print())
         .andReturn();
    }


    @Test
    public void testTimingOrder() throws Exception {
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).addFilters(new ReplacePriceFilter()).build();

        Merchant merchant = merchantMapper.selectOneByMid(84L);

        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), merchant.getOpenid(),
                merchant.getMpOpenid(), merchant.getPhone(), 1, merchant.getIslock());

        MerchantSubAccount account = subAccountService.selectOne("phone", "***********");

        merchantSubject.setUnionid(merchant.getUnionid());
        merchantSubject.setAdminId(merchant.getAdminId());
        merchantSubject.setDirect(merchant.getDirect());
        merchantSubject.setSkuShow(merchant.getSkuShow());
        merchantSubject.setSize(merchant.getSize());
        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));

        merchantSubject.setAccount(account);
        merchantSubject.setServer(merchant.getServer());

        //用户城市信息
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        merchantSubject.setArea(area);

        //客户类型
        Integer merchantType = 2;
        merchantSubject.setMerchantType(merchantType);

        mockHttpSession = new MockHttpSession();
        mockHttpSession.setAttribute(Global.MERCHANT_ID, 84L);
        mockHttpSession.setAttribute(Global.MERCHANT_SUBJECT,merchantSubject);
        mockHttpSession.setAttribute(Global.OPEN_ID,account.getOpenid());
        mockHttpSession.setAttribute(Global.ACCOUNT_ID,account.getAccountId());
        mockHttpSession.setAttribute("phone","***********");
        mockHttpSession.setAttribute("code","123456");

        JSONObject param = new JSONObject();
        param.put("mphone", "***********");
        param.put("mcontact", "chen");
        param.put("quantity", "5");
        param.put("timingRuleId", "146");
        DeliveryPlan dp1 = new DeliveryPlan();
        dp1.setDeliveryTime(LocalDate.of(2020, 12, 03));
        dp1.setQuantity(3);
        dp1.setContactId(98L);
        DeliveryPlan dp2 = new DeliveryPlan();
        dp2.setDeliveryTime(LocalDate.of(2020, 12, 07));
        dp2.setQuantity(2);
        dp2.setContactId(98L);
        List<DeliveryPlan> list = Lists.newArrayList(dp1, dp2);
        param.put("deliveryPlen", list);
        String content = param.toString();

        System.out.println(content);
        mvc.perform(
                MockMvcRequestBuilders.post("/order/timing")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(content)
                        .accept(MediaType.APPLICATION_JSON)
                        .session(mockHttpSession)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    @Test
    public void testCheckTimingOrder() throws Exception {
        JSONObject param = new JSONObject();
        param.put("quantity",5);
        param.put("timingRuleId",119);
        param.put("mphone","17681653200");
        String s = param.toString();
        String url = "/order/checkTimingOrder";
        //测试
        postTest(url,s);
    }

    @Test
    public void testPlaceOrder2() throws Exception {
        JSONObject param = new JSONObject();
        String url = "/order";
        param.put("contactId",197);
        param.put("mphone","17681653211");
        String s = param.toString();
        postTest(url, s);
    }


}
