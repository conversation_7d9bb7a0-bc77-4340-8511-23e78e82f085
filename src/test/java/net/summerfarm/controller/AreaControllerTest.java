package net.summerfarm.controller;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.task.cache.UpdateAreaCacheJob;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-09-06
 * @description
 */
public class AreaControllerTest extends BaseControllerTest {

    @Resource
    private AreaMapper areaMapper;

    @Test
    public void testMatch() {
        Merchant merchant = new Merchant();
        merchant.setProvince("浙江");
        merchant.setCity("杭州市");
        merchant.setArea("西湖区");
        matchAreaNo(merchant);
        System.err.println(merchant.getAreaNo());
    }

    @Resource
    private UpdateAreaCacheJob updateAreaCacheJob;

    @Test
    public void testUpdateAreaCacheJob() throws Exception {
        Assert.assertTrue(InstanceStatus.SUCCESS.equals(updateAreaCacheJob.processResult(null).getStatus()));
    }

    private void matchAreaNo(Merchant merchant) {
        List<Area> areas = areaMapper.selectSecond();
        for (Area area : areas) {
            boolean loopFlag = false;

            String[] sectionList = area.getMapSection().split(",");
            for (String s : sectionList) {
                String ssq = s.replaceAll("/", "");
                if ((merchant.getProvince() + merchant.getCity() + merchant.getArea()).contains(ssq)) {
                    merchant.setAreaNo(area.getAreaNo());
                    loopFlag = true;
                    break;
                }
            }

            if (loopFlag) {
                break;
            }
        }

        //区域匹配不到、匹配城市
        if (merchant.getAreaNo() == null) {
            for (Area area : areas) {
                boolean loopFlag = false;

                String[] sectionList = area.getMapSection().split(",");
                for (String s : sectionList) {
                    String ss = s.replaceAll("/", "").replace("其他区域", "");
                    boolean condition = area.getMapSection().contains("其他区域") || StringUtils.countOccurrencesOf(area.getMapSection(), "/") == 1;
                    if ((merchant.getProvince() + merchant.getCity()).contains(ss) && condition) {
                        merchant.setAreaNo(area.getAreaNo());
                        loopFlag = true;
                        break;
                    }
                }

                if (loopFlag) {
                    break;
                }
            }
        }
    }
}
