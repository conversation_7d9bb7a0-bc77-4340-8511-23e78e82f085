package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.mall.controller.ai.AiAssistantController;
import net.summerfarm.mall.model.dto.ai.QueryProductQuestionsRequest;
import net.summerfarm.mall.model.vo.ai.ProductQuestionsVO;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class AiAssistantControllerTest  extends BaseControllerTest {

    @Resource
    private AiAssistantController aiAssistantController;

    @Test
    public void test(){
        QueryProductQuestionsRequest request = new QueryProductQuestionsRequest();
        request.setSku("123");
        CommonResult<ProductQuestionsVO> result = aiAssistantController.queryProductQuestions(request);
        log.info("result:{}", JSONObject.toJSONString(result));
    }
}
