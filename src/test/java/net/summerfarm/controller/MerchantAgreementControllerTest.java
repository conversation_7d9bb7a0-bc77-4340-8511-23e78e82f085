package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.mall.controller.merchantAgreement.MerchantAgreementController;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.dto.agreement.QueryAgreementByVersionRequest;
import net.summerfarm.mall.model.dto.agreement.SignAgreementRequest;
import net.summerfarm.mall.model.vo.agreement.MerchantAgreementVO;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;

import javax.annotation.Resource;

@Slf4j
public class MerchantAgreementControllerTest  extends BaseControllerTest {

    @Resource
    private MerchantAgreementController merchantAgreementController;

    @Resource
    private AreaMapper areaMapper;
    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private MerchantMapper merchantMapper;

    @Test
    public void testqueryAgreementByVersion(){
        QueryAgreementByVersionRequest request = new QueryAgreementByVersionRequest();
        request.setAgreementType(1);
        request.setAgreementVersion("1.0.0");
        CommonResult<MerchantAgreementVO> result = merchantAgreementController.queryAgreementByVersion(request);
        log.info("result:{}", JSONObject.toJSONString(result));
    }

    @Test
    public void testsignAgreement(){
        SignAgreementRequest request = new SignAgreementRequest();
        request.setAgreementType(1);
        request.setAgreementVersion("1.0.0");
        CommonResult<Boolean> result = merchantAgreementController.signAgreement(request);
        log.info("result:{}", JSONObject.toJSONString(result));
    }

}
