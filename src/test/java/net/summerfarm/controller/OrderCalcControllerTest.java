package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseMvcTest;
import net.summerfarm.mall.model.domain.Trolley;
import org.elasticsearch.common.util.CollectionUtils;
import org.junit.Test;
import org.springframework.test.web.servlet.MvcResult;

import java.util.ArrayList;
import java.util.List;

public class OrderCalcControllerTest extends BaseMvcTest {

    @Test
    public void preOrderTest() throws Exception {
        JSONObject params = new JSONObject();
        params.put("accountId", "49");
        params.put("contactId", "85");

        List<Integer> list = new ArrayList<>();
        list.add(1449208);
        list.add(1449217);
        params.put("merchantCouponId", list);

        Trolley trolley = new Trolley();
        trolley.setProductType(0);
        trolley.setQuantity(1);
        trolley.setSku("***********");
        trolley.setSuitId(0);
        params.put("orderNow", CollectionUtils.newSingletonArrayList(trolley));
        MvcResult mvcResult = postTest("/order/pre-order/v2", params.toString());
    }

    @Test
    public void placeOrderTest() throws Exception {
        JSONObject params = new JSONObject();
        params.put("accountId", "49");
        params.put("contactId", "85");
//        params.put("timeFrameName", "SECOND");

        List<Integer> list = new ArrayList<>();
        list.add(1449208);
        list.add(1449217);
        params.put("merchantCouponId", list);

        Trolley trolley = new Trolley();
        trolley.setProductType(0);
        trolley.setQuantity(1);
        trolley.setSku("***********");
        trolley.setSuitId(0);
        params.put("orderNow", CollectionUtils.newSingletonArrayList(trolley));

        MvcResult mvcResult = postTest("/order/place-order/v2", params.toString());
    }

    @Override
    public Long loginMId() {
        return 1479L;
    }
}
