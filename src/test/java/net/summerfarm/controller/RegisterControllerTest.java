package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.service.MerchantService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Base64;

import static org.junit.Assert.*;
import static reactor.core.publisher.Mono.when;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-11
 */
public class RegisterControllerTest extends BaseControllerTest {

    @Resource
    MerchantService merchantService;

    @InjectMocks
    private RequestHolder requestHolder;
    @Test
    public void claimMerchant() throws Exception {
        MultiValueMap<String, String> params = new HttpHeaders();
        params.put("yzm", Arrays.asList("123456"));
        params.put("type",Arrays.asList(""));
        String base64encode = new String(Base64.getEncoder().encode("1".getBytes()));
        postTest("/register/claim/"+ base64encode,params);
    }

    @Test
    public void register() throws Exception {
        //     "/register/VerifyBL?doorPic=fe-biz/xm-mall/vm7jrbgxfpo&mname=幸运网红定制蛋糕&mcontact=幸运网红定制蛋糕&province=湖北&city=武汉市&area=武昌区&address=
        //        武汉中学宿舍东侧50米+忠孝门28号楼&phone=15119064310&invitecode=seeegj&houseNumber=附22&type=
        //        面包蛋糕&doorPicOcr=幸运网红定制蛋糕&poiNote=114.314986,30.545318",params);
        Merchant merchant = new Merchant();
        merchant.setPhone("1538278806");
        merchant.setOpenid("abcdefigshhhh");
        merchant.setUnionid("test_unionid");
        merchant.setInvitecode("seeegj");
        merchant.setDoorPicOcr("幸运网红定制蛋糕");
        merchant.setAddress("忠孝门28号楼");
        merchant.setMname("幸运网红定制蛋糕");
        merchant.setHouseNumber("武汉中学宿舍东侧50米");
        merchant.setCity("武汉市");
        merchant.setPoiNote("114.314986,30.545318");
        merchantService.verifyBL(merchant, true);
    }
}