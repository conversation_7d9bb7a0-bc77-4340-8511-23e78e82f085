package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.model.domain.OrderItem;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.summerfarm.mall.service.TrolleyService;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-08-14
 * @description
 */
public class MerchantSubAccountTest extends BaseControllerTest {
    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private TrolleyService trolleyService;


    @Test
    public void selectAccountList() {
       subAccountService.selectAccountList(11739L, 11837L);
    }

    @Test
    public void selectAccountInfo(){
        RequestHolder.getSession().setAttribute(Global.MERCHANT_ID, 11739L);
        RequestHolder.getSession().setAttribute(Global.ACCOUNT_ID, 11837L);
        subAccountService.selectAccountInfo(11838L);
    }

    @Test
    public void changeManger(){
        RequestHolder.getSession().setAttribute(Global.MERCHANT_ID, 11739L);
        RequestHolder.getSession().setAttribute(Global.ACCOUNT_ID, 11837L);
        subAccountService.changeManger(11838L);
    }

    @Test
    public void delAccount(){
        RequestHolder.getSession().setAttribute(Global.MERCHANT_ID, 11739L);
        RequestHolder.getSession().setAttribute(Global.ACCOUNT_ID, 11838L);
        subAccountService.deleteAccount(11837L);
    }

    @Test
    public void audit(){
        RequestHolder.getSession().setAttribute(Global.MERCHANT_ID, 11739L);
        RequestHolder.getSession().setAttribute(Global.ACCOUNT_ID, 11838L);
        subAccountService.auditAccount(11837L, 1);
    }

    @Test
    public void read(){
        RequestHolder.getSession().setAttribute(Global.ACCOUNT_ID, 11838L);
        subAccountService.readFirstPopView();
    }

    @Test
    public void discount(){
        List<OrderItem> orderItems = new ArrayList<>();
        OrderItem orderItem = new OrderItem();
        orderItem.setPrice(BigDecimal.valueOf(12));
        orderItem.setAmount(1);
        orderItem.setSku("***********");
        orderItems.add(orderItem);
        trolleyService.discountCardUsable(orderItems,1001,null, 1212L);
    }
}
