package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;


/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/3/29
 */
public class MallControllerTest extends BaseControllerTest{

    @Test
    public void timeFrame() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/merchant/member-select").accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void product() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/product/1/10").accept(MediaType.APPLICATION_JSON)
                .session(mockHttpSession)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void productInfo() throws Exception{
        //  /product-info/{pdId}
        getTest("/product-info/1");
    }

    @Test
    public void selectPopularSku() throws Exception {
        getTest("/temporary-activity/popular-skuList?tableName=品牌热榜&brandName=莫林");
    }
}