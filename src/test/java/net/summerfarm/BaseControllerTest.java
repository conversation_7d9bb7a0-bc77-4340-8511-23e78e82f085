package net.summerfarm;

import net.summerfarm.mall.MallApplication;
import net.summerfarm.mall.common.filter.ReplacePriceFilter;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.MerchantSubAccount;
import net.summerfarm.mall.model.vo.MerchantSubject;
import net.summerfarm.mall.service.MerchantSubAccountService;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * @Package: PACKAGE_NAME
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/10/21
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MallApplication.class)
//@WebAppConfiguration
public class BaseControllerTest {

    protected static final Logger logger = LoggerFactory.getLogger(BaseControllerTest.class);

    public static final MediaType APPLICATION_JSON_UTF8 = new MediaType(MediaType.APPLICATION_JSON.getType(), MediaType.APPLICATION_JSON.getSubtype(), StandardCharsets.UTF_8);


    @Autowired
    protected WebApplicationContext webApplicationContext;

    protected MockMvc mvc;

    protected MockHttpSession mockHttpSession;

    protected MerchantSubject ms;

    @Autowired
    private MerchantMapper merchantMapper;

    @Resource
    private MerchantSubAccountService subAccountService;
    @Resource
    private AreaMapper areaMapper;

    @Before
    public void setUp()  {
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).addFilters(new ReplacePriceFilter()).build();

        Merchant merchant = merchantMapper.selectOneByMid(1452L);

        MerchantSubject merchantSubject = new MerchantSubject(merchant.getmId(), merchant.getMname(), merchant.getOpenid(),
                merchant.getMpOpenid(), merchant.getPhone(), 1, merchant.getIslock());

        MerchantSubAccount account = subAccountService.selectOne("phone", "***********");

        merchantSubject.setUnionid(merchant.getUnionid());
        merchantSubject.setAdminId(merchant.getAdminId());
        merchantSubject.setDirect(merchant.getDirect());
        merchantSubject.setSkuShow(merchant.getSkuShow());
        merchantSubject.setSize(merchant.getSize());
        merchantSubject.setRegisterTime(DateUtils.date2LocalDateTime(merchant.getRegisterTime()));

        merchantSubject.setAccount(account);
        merchantSubject.setServer(merchant.getServer());


        //用户城市信息
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        merchantSubject.setArea(area);

        //客户类型
        Integer merchantType = 1;
        merchantSubject.setMerchantType(merchantType);

        mockHttpSession = new MockHttpSession();
        mockHttpSession.setAttribute(Global.MERCHANT_ID, 1452L);
        mockHttpSession.setAttribute(Global.MERCHANT_SUBJECT,merchantSubject);
        mockHttpSession.setAttribute(Global.OPEN_ID,account.getOpenid());
        mockHttpSession.setAttribute(Global.ACCOUNT_ID,account.getAccountId());
        mockHttpSession.setAttribute("phone","***********");
        mockHttpSession.setAttribute("code","123456");
    }

    protected MvcResult postTest(String url, MultiValueMap<String, String> params) throws Exception {
        return doTest(url, RequestMethod.POST, params, "", MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult postTest(String url, String jsonData) throws Exception {
        return doTest(url, RequestMethod.POST, null, jsonData,APPLICATION_JSON_UTF8);
    }

    protected MvcResult getTest(String url) throws Exception {
        return doTest(url, RequestMethod.GET, null, "", MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult getTest(String url, MultiValueMap<String, String> params ) throws Exception {
        return doTest(url, RequestMethod.GET, params, "", MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult doTest(String url, RequestMethod requestMethod, MultiValueMap<String, String> params, String jsonData, MediaType mediaType) throws Exception {
        if (CollectionUtils.isEmpty(params)) {
            params = new LinkedMultiValueMap<>();
        }
        if (jsonData == null) {
            jsonData = "";
        }
        MockHttpServletRequestBuilder builder = null;
        switch (requestMethod){
            case POST: builder =  MockMvcRequestBuilders.post(url); break;
            case PUT: builder =  MockMvcRequestBuilders.put(url); break;
            case DELETE: builder =  MockMvcRequestBuilders.delete(url); break;
            default: builder = MockMvcRequestBuilders.get(url);break;
        }
        return mvc.perform(
                builder.session(mockHttpSession)
                        .accept(MediaType.APPLICATION_JSON)
                        .params(params)
                        .contentType(mediaType)
                        .content(jsonData)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
