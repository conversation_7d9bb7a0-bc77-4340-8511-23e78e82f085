server:
  tomcat:
    accept-count: 1000
    max-connections: 10000
    max-threads: 800
    min-spare-threads: 100
  servlet:
    session:
      timeout: 3600
  port: 80
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: test
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_mall
    secret-key: ''
    sendMsgTimeout: 10000
scheduling:
  local:
    ip: ********
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
mall:
  domain:
    name: dev3h5.summerfarm.net
spring:
  application:
    id: summerfarm-mall
    name: summerfarm-mall
  schedulerx2:
    appKey: CFwr/XWm0RovQ4SrF3O7Bw==
    endpoint: acm.aliyun.com
    groupId: mall
    namespace: d470595b-6520-4833-a158-239340a08eb2
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 5000 # 连接超时时间（毫秒）
    database: 3
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 2
    jedis:
      pool:
        max-active: 30 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://dev3h5.summerfarm.net
wechat:
  app:
    id: wx5b32661e10a8057d
    secret: cd238ced60c5443d9491e0081ae7a912
  mp-app:
    id: wx0234b1d4eb212e12
    secret: 7779dbf6349ca85212b05435dfc38716

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.mall.model.domain
  mapper-locations: classpath:net.summerfarm.mall.mapper/*.xml

# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

##启用shutdown
#endpoints:
#  shutdown:
#    enabled: true
#    #禁用密码验证
#    sensitive: false
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: c26bc4c2-bd51-4aae-a170-1f04b9c52987
  protocol:
    id: dubbo
    name: dubbo
    port: -1
  provider:
    version: 1.0.0
    group: offline
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: bd543515-d448-423f-baec-ec069c924c12
xm:
  log:
    enable: true
    resp: true