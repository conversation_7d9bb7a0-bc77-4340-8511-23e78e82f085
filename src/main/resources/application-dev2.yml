
server:
  tomcat:
    accept-count: 1000
    max-connections: 10000
    max-threads: 800
    min-spare-threads: 100
  servlet:
    session:
      timeout: 3600
  port: 80
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: dev2
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_mall
    secret-key: ''
    sendMsgTimeout: 10000
scheduling:
  local:
    ip: ********
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
mall:
  domain:
    name: dev2h5.summerfarm.net
spring:
  application:
    id: summerfarm-mall
    name: summerfarm-mall
  schedulerx2:
    appKey: CFwr/XWm0RovQ4SrF3O7Bw==
    endpoint: acm.aliyun.com
    groupId: mall
    namespace: d470595b-6520-4833-a158-239340a08eb2
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 5000 # 连接超时时间（毫秒）
    database: 2
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）        
        
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://dev2h5.summerfarm.net
pop:
  mall:
    domain: https://dev2shunluda.cosfo.cn
wechat:
  app:
    id: wxfc419e72b4747b33
    secret: ea4a5e2f096d2ce177bc7a3d1bbb07cd
  mp-app:
    id: wx0234b1d4eb212e12
    secret: 7779dbf6349ca85212b05435dfc38716
  pop-app:
    id: wxdc920b47380a0ae4
    secret: dcaa85b59ca267a3606f08a0b0fd95ce
  pop-mp-app:
    id: wx99168dd45561f6f1
    secret: 6abfde5743fd36982132515ab4aed7ee

## Mybatis 配置
mybatis:
  type-aliases-package: net.summerfarm.mall.model.domain
  mapper-locations: classpath:net.summerfarm.mall.mapper/*.xml

# 日志配置
logging:
  pattern:
    console: "%d - %msg%n"
  level:
    root:  INFO
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm: INFO

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

##启用shutdown
#endpoints:
#  shutdown:
#    enabled: true
#    #禁用密码验证
#    sensitive: false
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 800ed4d6-a4fd-4345-86dd-8a4cc70c9bda
xm:
  log:
    enable: true
    resp: true
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752