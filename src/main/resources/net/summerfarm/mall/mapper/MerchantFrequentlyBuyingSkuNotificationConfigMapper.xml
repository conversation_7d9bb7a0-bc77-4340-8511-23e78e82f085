<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantFrequentlyBuyingSkuNotificationConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSkuNotificationConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="special_offer" jdbcType="INTEGER" property="specialOffer" />
    <result column="goods_arrived" jdbcType="INTEGER" property="goodsArrived" />
  </resultMap>
  
  
  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, special_offer, goods_arrived
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_frequently_buying_sku_notification_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByMid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_frequently_buying_sku_notification_config
    where m_id = #{mId,jdbcType=BIGINT}
    limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_frequently_buying_sku_notification_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
 
  <insert id="insert" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSkuNotificationConfig">
    insert into merchant_frequently_buying_sku_notification_config (
      m_id, special_offer, goods_arrived
      )
    values (
        #{mId,jdbcType=BIGINT}, #{specialOffer,jdbcType=INTEGER}, #{goodsArrived,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSkuNotificationConfig">
    insert into merchant_frequently_buying_sku_notification_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="specialOffer != null">
        special_offer,
      </if>
      <if test="goodsArrived != null">
        goods_arrived,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="specialOffer != null">
        #{specialOffer,jdbcType=INTEGER},
      </if>
      <if test="goodsArrived != null">
        #{goodsArrived,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSkuNotificationConfig">
    update merchant_frequently_buying_sku_notification_config
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="specialOffer != null">
        special_offer = #{specialOffer,jdbcType=INTEGER},
      </if>
      <if test="goodsArrived != null">
        goods_arrived = #{goodsArrived,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.frequentSkuPool.MerchantFrequentlyBuyingSkuNotificationConfig">
    update merchant_frequently_buying_sku_notification_config
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      m_id = #{mId,jdbcType=BIGINT},
      special_offer = #{specialOffer,jdbcType=INTEGER},
      goods_arrived = #{goodsArrived,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="countByMId" resultType="int">
  select count(*)
    from merchant_frequently_buying_sku_notification_config
    where m_id = #{mId,jdbcType=BIGINT}
</select>
</mapper>